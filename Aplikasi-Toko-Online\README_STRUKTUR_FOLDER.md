# Struktur Folder Aplikasi Toko Online

Aplikasi ini telah diorganisir dengan struktur folder yang lebih baik untuk memudahkan pengembangan dan pemeliharaan.

## 📁 Struktur Utama

```
Aplikasi-Toko-Online/
├── Forms/                  # Semua form aplikasi
│   ├── Admin/              # Form khusus admin
│   ├── Auth/               # Form login & register
│   └── User/               # Form untuk pengguna biasa
├── Modules/                # Modul dan kelas utilitas
├── Database/               # Script SQL dan database
└── My Project/             # File konfigurasi project
```

## 📋 Detail Folder

### 1. Forms/
Berisi semua form aplikasi yang dikelompokkan berdasarkan peran pengguna:

#### 🔐 Auth/
- `FormLogin.vb` - Form login pengguna
- `FormRegister.vb` - Form registrasi pengguna baru

#### 👤 User/
- `FormMain.vb` - Form utama aplikasi (dashboard user)
- `FormCart.vb` - Form keranjang belanja
- `FormCheckout.vb` - Form checkout/pembayaran
- `FormMyOrders.vb` - Form daftar pesanan user
- `FormOrderDetail.vb` - Form detail pesanan
- `FormPaymentGateway.vb` - Form gateway pembayaran
- `FormPaymentHistory.vb` - Form riwayat pembayaran
- `FormViewProducts.vb` - Form tampilan produk
- `FormWishlist.vb` - Form wishlist/daftar keinginan

#### 👨‍💼 Admin/
- `FormAdminDashboard.vb` - Dashboard admin
- `FormAdminOrders.vb` - Kelola pesanan
- `FormAdminProducts.vb` - Kelola produk
- `FormAdminUsers.vb` - Kelola pengguna
- `FormAddProduct.vb` - Tambah produk baru
- `FormEditProduct.vb` - Edit produk
- `FormDeleteProduct.vb` - Hapus produk

### 2. Modules/
Berisi modul dan kelas utilitas yang digunakan di seluruh aplikasi:
- `ModulKoneksi.vb` - Modul untuk mengelola koneksi database
- `OrderStatusManager.vb` - Kelas untuk mengelola status pesanan

### 3. Database/
Berisi script SQL dan file database:
- `database_orders.sql` - Script pembuatan tabel orders
- `update_status_to_success.sql` - Update status 'delivered' ke 'success'
- Dan file SQL lainnya

## 🔄 Alur Kerja Aplikasi

1. **Login/Register** (Forms/Auth/)
2. **Dashboard User** (Forms/User/FormMain.vb)
3. **Belanja & Checkout** (Forms/User/)
4. **Manajemen Pesanan** (Forms/User/ & Forms/Admin/)

## 🛠️ Cara Menggunakan

1. **Buka Project di Visual Studio**
2. **Jalankan Script Database** dari folder Database/
3. **Build & Run Aplikasi**

## 📝 Catatan Penting

- Semua koneksi database menggunakan `ModulKoneksi.vb`
- Status pesanan dikelola oleh `OrderStatusManager.vb`
- Form login adalah entry point aplikasi

## 🔄 Perubahan Status Order

Status order dikelola dalam database dengan nilai:
- `pending` - Menunggu konfirmasi
- `processing` - Sedang diproses
- `shipped` - Sedang dikirim
- `success` - Berhasil selesai
- `cancelled` - Dibatalkan
