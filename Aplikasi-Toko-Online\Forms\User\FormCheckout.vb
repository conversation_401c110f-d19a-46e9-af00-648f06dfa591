Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormCheckout
    Private cartItems As List(Of FormMain.CartItem)
    Private currentUserID As Integer
    Private totalAmount As Decimal
    
    Public Sub New(items As List(Of FormMain.CartItem), userID As Integer)
        InitializeComponent()
        cartItems = items
        currentUserID = userID
        totalAmount = items.Sum(Function(item) item.Total)
    End Sub
    
    Private Sub FormCheckout_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Checkout"
        Me.Size = New Size(900, 750)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)

        ' Set form properties untuk ukuran absolute dan tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(900, 750)
        Me.MaximumSize = New Size(900, 750)

        CreateUI()
        LoadUserInfo()
    End Sub
    
    Private Sub CreateUI()
        ' Header Panel dengan gradient
        Dim pnlHeader As New Panel With {
            .Size = New Size(Me.Width, 100),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(41, 128, 185),
            .Dock = DockStyle.Top
        }

        ' Gradient effect untuk header
        AddHandler pnlHeader.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlHeader.Width, pnlHeader.Height)
            Using brush As New Drawing2D.LinearGradientBrush(rect, Color.FromArgb(52, 152, 219), Color.FromArgb(41, 128, 185), Drawing2D.LinearGradientMode.Vertical)
                e.Graphics.FillRectangle(brush, rect)
            End Using
        End Sub

        Dim lblTitle As New Label With {
            .Text = "🛒 CHECKOUT",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(40, 30),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)
        Me.Controls.Add(pnlHeader)
        
        ' Shipping Address Panel dengan styling modern
        Dim pnlShipping As New Panel With {
            .Location = New Point(40, 130),
            .Size = New Size(800, 180),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }

        ' Tambahkan border dan shadow
        AddHandler pnlShipping.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlShipping.Width - 1, pnlShipping.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblShippingTitle As New Label With {
            .Text = "📍 ALAMAT PENGIRIMAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True
        }

        Dim txtAddress As New TextBox With {
            .Name = "txtAddress",
            .Location = New Point(25, 60),
            .Size = New Size(750, 90),
            .Multiline = True,
            .Font = New Font("Segoe UI", 12),
            .BorderStyle = BorderStyle.FixedSingle,
            .BackColor = Color.FromArgb(250, 250, 250)
        }
        
        pnlShipping.Controls.AddRange({lblShippingTitle, txtAddress})
        Me.Controls.Add(pnlShipping)
        
        ' Payment Method Panel dengan styling modern
        Dim pnlPayment As New Panel With {
            .Location = New Point(40, 330),
            .Size = New Size(800, 140),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }

        ' Tambahkan border dan shadow
        AddHandler pnlPayment.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlPayment.Width - 1, pnlPayment.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblPaymentTitle As New Label With {
            .Text = "💳 METODE PEMBAYARAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True
        }

        Dim cmbPayment As New ComboBox With {
            .Name = "cmbPayment",
            .Location = New Point(25, 60),
            .Size = New Size(300, 35),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 12),
            .FlatStyle = FlatStyle.Flat
        }
        cmbPayment.Items.AddRange({"Transfer Bank", "COD (Cash on Delivery)", "E-Wallet", "Kartu Kredit"})
        cmbPayment.SelectedIndex = 0
        
        pnlPayment.Controls.AddRange({lblPaymentTitle, cmbPayment})
        Me.Controls.Add(pnlPayment)
        
        ' Order Summary Panel dengan styling modern
        Dim pnlSummary As New Panel With {
            .Location = New Point(40, 490),
            .Size = New Size(800, 120),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }

        ' Tambahkan border dan shadow
        AddHandler pnlSummary.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlSummary.Width - 1, pnlSummary.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblSummaryTitle As New Label With {
            .Text = "📋 RINGKASAN PESANAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True
        }

        Dim lblItemCount As New Label With {
            .Text = $"Jumlah Item: {cartItems.Sum(Function(item) item.Quantity)}",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(25, 55),
            .AutoSize = True
        }

        Dim lblTotal As New Label With {
            .Text = $"Total Pembayaran: Rp {totalAmount:N0}",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(25, 80),
            .AutoSize = True
        }
        
        pnlSummary.Controls.AddRange({lblSummaryTitle, lblItemCount, lblTotal})
        Me.Controls.Add(pnlSummary)
        
        ' Action Buttons dengan styling modern - posisi tengah
        Dim btnCancel As New Button With {
            .Text = "❌ Batal",
            .Size = New Size(150, 50),
            .Location = New Point(280, 630),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()

        Dim btnPlaceOrder As New Button With {
            .Text = "✅ Buat Pesanan",
            .Size = New Size(180, 50),
            .Location = New Point(450, 630),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnPlaceOrder.FlatAppearance.BorderSize = 0
        AddHandler btnPlaceOrder.Click, AddressOf BtnPlaceOrder_Click
        
        Me.Controls.AddRange({btnCancel, btnPlaceOrder})
    End Sub
    
    Private Sub LoadUserInfo()
        Try
            koneksi()
            
            perintahSQL = New MySqlCommand("SELECT address FROM users WHERE user_id = @userID", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
            pembacaData = perintahSQL.ExecuteReader()

            If pembacaData.Read() AndAlso Not IsDBNull(pembacaData("address")) Then
                Dim txtAddress As TextBox = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)
                If txtAddress IsNot Nothing Then
                    txtAddress.Text = pembacaData("address").ToString()
                    txtAddress.ForeColor = Color.Black
                End If
            Else
                Dim txtAddress As TextBox = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)
                If txtAddress IsNot Nothing Then
                    txtAddress.Text = "Masukkan alamat lengkap pengiriman..."
                    txtAddress.ForeColor = Color.Gray
                End If
            End If
            
            pembacaData.Close()
            tutupKoneksi()
            
        Catch ex As Exception
            MessageBox.Show("Error loading user info: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub BtnPlaceOrder_Click(sender As Object, e As EventArgs)
        Dim txtAddress As TextBox = CType(Me.Controls.Find("txtAddress", True).FirstOrDefault(), TextBox)
        Dim cmbPayment As ComboBox = CType(Me.Controls.Find("cmbPayment", True).FirstOrDefault(), ComboBox)

        ' Validasi input
        If String.IsNullOrWhiteSpace(txtAddress.Text) Then
            MessageBox.Show("Alamat pengiriman harus diisi!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtAddress.Focus()
            Return
        End If

        If cmbPayment.SelectedIndex = -1 Then
            MessageBox.Show("Pilih metode pembayaran!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbPayment.Focus()
            Return
        End If

        ' Konfirmasi sebelum checkout
        Dim confirmMsg As String = $"Buat pesanan Rp {totalAmount:N0}?"

        If MessageBox.Show(confirmMsg, "Konfirmasi",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Return
        End If

        ' Set loading state
        SetLoadingState(True)
        
        Try
            koneksi()
            Dim trans As MySqlTransaction = koneksiDatabase.BeginTransaction()

            Try
                ' Validasi data sebelum insert
                If currentUserID <= 0 Then
                    MessageBox.Show("Error: User ID tidak valid!", "Validasi Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                If totalAmount <= 0 Then
                    MessageBox.Show("Error: Total amount tidak valid!", "Validasi Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                ' Insert order (dengan payment_method)
                perintahSQL = New MySqlCommand("INSERT INTO orders (user_id, order_date, total_amount, order_status, shipping_address, payment_method) " &
                                      "VALUES (@userID, @orderDate, @totalAmount, @status, @address, @payment)", koneksiDatabase, trans)
                perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
                perintahSQL.Parameters.AddWithValue("@orderDate", DateTime.Now)
                perintahSQL.Parameters.AddWithValue("@totalAmount", totalAmount)
                perintahSQL.Parameters.AddWithValue("@status", "pending")
                perintahSQL.Parameters.AddWithValue("@address", txtAddress.Text.Trim())
                perintahSQL.Parameters.AddWithValue("@payment", cmbPayment.SelectedItem.ToString())
                perintahSQL.ExecuteNonQuery()

                ' Get order ID
                Dim orderID As Integer = Convert.ToInt32(perintahSQL.LastInsertedId)
                
                ' Insert order details (dengan subtotal)
                For Each item In cartItems
                    perintahSQL = New MySqlCommand("INSERT INTO order_details (order_id, product_id, quantity, price, subtotal) " &
                                          "VALUES (@orderID, @productID, @quantity, @price, @subtotal)", koneksiDatabase, trans)
                    perintahSQL.Parameters.AddWithValue("@orderID", orderID)
                    perintahSQL.Parameters.AddWithValue("@productID", item.ProductID)
                    perintahSQL.Parameters.AddWithValue("@quantity", item.Quantity)
                    perintahSQL.Parameters.AddWithValue("@price", item.Price)
                    perintahSQL.Parameters.AddWithValue("@subtotal", item.Total)
                    perintahSQL.ExecuteNonQuery()

                    ' Update product stock
                    perintahSQL = New MySqlCommand("UPDATE products SET stock = stock - @quantity WHERE product_id = @productID", koneksiDatabase, trans)
                    perintahSQL.Parameters.AddWithValue("@quantity", item.Quantity)
                    perintahSQL.Parameters.AddWithValue("@productID", item.ProductID)
                    perintahSQL.ExecuteNonQuery()
                Next
                
                trans.Commit()
                tutupKoneksi()

                ' Reset loading state
                SetLoadingState(False)

                ' Play success sound
                Try
                    My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Exclamation)
                Catch
                    ' Ignore sound errors
                End Try

                ' Success message
                Dim successMsg As String = $"Pesanan #{orderID} berhasil dibuat!"

                MessageBox.Show(successMsg, "Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)

                ' Tanya apakah ingin lanjut ke pembayaran
                Dim paymentMsg As String = "Lanjut ke pembayaran sekarang?"

                If MessageBox.Show(paymentMsg, "Pembayaran",
                                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then

                    ' Buka form payment gateway
                    Dim frmPayment As New FormPaymentGateway(orderID, cmbPayment.SelectedItem.ToString(), totalAmount)
                    If frmPayment.ShowDialog() = DialogResult.OK Then
                        MessageBox.Show("Pembayaran berhasil!", "Berhasil", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    End If
                Else
                    ' User memilih No - beri informasi cara bayar nanti
                    MessageBox.Show($"💡 Pesanan #{orderID} tersimpan!" & vbCrLf & vbCrLf &
                                   "Anda bisa melanjutkan pembayaran kapan saja melalui:" & vbCrLf &
                                   "• Menu 'Pesanan Saya' → Klik tombol 'Bayar'" & vbCrLf &
                                   "• Menu 'Pembayaran' di header" & vbCrLf & vbCrLf &
                                   "⚠️ Pesanan akan dibatalkan otomatis dalam 10 menit jika tidak dibayar.",
                                   "Pesanan Tersimpan", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If

                Me.DialogResult = DialogResult.OK
                Me.Close()

            Catch ex As Exception
                trans.Rollback()

                ' Reset loading state
                SetLoadingState(False)

                ' Play error sound
                Try
                    My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Hand)
                Catch
                    ' Ignore sound errors
                End Try

                ' Error message
                Dim errorMsg As String = "Checkout gagal! Silakan coba lagi."

                MessageBox.Show(errorMsg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End Try

        Catch ex As Exception
            ' Reset loading state
            SetLoadingState(False)

            ' Play error sound
            Try
                My.Computer.Audio.PlaySystemSound(Media.SystemSounds.Hand)
            Catch
                ' Ignore sound errors
            End Try

            ' Connection error message
            Dim connectionErrorMsg As String = "Koneksi bermasalah! Periksa internet Anda."

            MessageBox.Show(connectionErrorMsg, "Koneksi Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            If koneksiDatabase IsNot Nothing AndAlso koneksiDatabase.State = ConnectionState.Open Then
                tutupKoneksi()
            End If

            ' Pastikan loading state direset
            SetLoadingState(False)
        End Try
    End Sub

    ' Helper methods untuk loading state
    Private Sub SetLoadingState(isLoading As Boolean)
        Dim btnPlaceOrder As Button = CType(Me.Controls.Find("btnPlaceOrder", True).FirstOrDefault(), Button)
        Dim btnCancel As Button = CType(Me.Controls.Find("btnCancel", True).FirstOrDefault(), Button)

        If btnPlaceOrder IsNot Nothing Then
            If isLoading Then
                btnPlaceOrder.Enabled = False
                btnPlaceOrder.Text = "⏳ Memproses Pesanan..."
                btnPlaceOrder.BackColor = Color.Gray
            Else
                btnPlaceOrder.Enabled = True
                btnPlaceOrder.Text = "✅ Buat Pesanan"
                btnPlaceOrder.BackColor = Color.FromArgb(46, 204, 113)
            End If
        End If

        If btnCancel IsNot Nothing Then
            btnCancel.Enabled = Not isLoading
        End If

        ' Disable semua input controls saat loading
        For Each ctrl As Control In Me.Controls
            If TypeOf ctrl Is TextBox OrElse TypeOf ctrl Is ComboBox Then
                ctrl.Enabled = Not isLoading
            ElseIf TypeOf ctrl Is Panel Then
                For Each subCtrl As Control In ctrl.Controls
                    If TypeOf subCtrl Is TextBox OrElse TypeOf subCtrl Is ComboBox Then
                        subCtrl.Enabled = Not isLoading
                    End If
                Next
            End If
        Next

        Me.Cursor = If(isLoading, Cursors.WaitCursor, Cursors.Default)
    End Sub
End Class
