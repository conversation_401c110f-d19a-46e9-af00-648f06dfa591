-- <PERSON><PERSON>t untuk menambahkan kolom role ke tabel users
-- Jalankan script ini di MySQL untuk menambahkan kolom role

USE db_ecommerce;

-- Tambahkan kolom role ke tabel users
ALTER TABLE users ADD COLUMN IF NOT EXISTS role ENUM('user', 'admin') DEFAULT 'user';

-- Set default role untuk user yang sudah ada
UPDATE users SET role = 'user' WHERE role IS NULL;

-- Buat satu user admin untuk testing (ganti username se<PERSON><PERSON> kebutuhan)
-- Uncomment baris di bawah dan ganti 'admin' dengan username yang di<PERSON><PERSON>an
-- UPDATE users SET role = 'admin' WHERE username = 'admin';

-- Atau insert user admin baru jika belum ada
-- INSERT INTO users (username, password, full_name, email, phone, address, role, created_at) 
-- VALUES ('admin', 'admin123', 'Administrator', '<EMAIL>', '081234567890', 'Jakarta', 'admin', NOW());

-- Cek struktur tabel users setelah penambahan kolom
DESCRIBE users;

-- Cek data users dengan role
SELECT user_id, username, full_name, email, role FROM users;
