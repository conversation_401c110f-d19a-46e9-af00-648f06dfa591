-- Script SQL untuk mengupdate database yang sudah ada
-- Mengganti status 'delivered' dengan 'success' dan menghapus 'delivered' dari ENUM

USE db_ecommerce;

-- 1. Backup data yang ada (opsional)
-- CREATE TABLE orders_backup AS SELECT * FROM orders;

-- 2. Update semua order dengan status 'delivered' menjadi 'success'
UPDATE orders SET order_status = 'success' WHERE order_status = 'delivered';

-- 3. Alter table untuk menghapus 'delivered' dari ENUM dan hanya menggunakan 'success'
-- Catatan: Pastikan langkah 2 sudah dijalankan terlebih dahulu (tidak ada data 'delivered')

ALTER TABLE orders MODIFY COLUMN order_status
ENUM('pending', 'processing', 'shipped', 'success', 'cancelled') DEFAULT 'pending';

-- 4. Verifikasi perubahan
SELECT order_status, COUNT(*) as jumlah 
FROM orders 
GROUP BY order_status 
ORDER BY order_status;

-- 5. <PERSON><PERSON><PERSON><PERSON> semua order dengan status baru
SELECT order_id, user_id, order_status, order_date, total_amount 
FROM orders 
ORDER BY order_date DESC 
LIMIT 10;

-- Catatan:
-- - Status 'success' = Berhasil/Selesai (menggantikan 'delivered')
-- - Status 'delivered' telah dihapus sepenuhnya dari sistem
-- - Aplikasi hanya menggunakan 'success' untuk order yang berhasil dibayar
