﻿Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormCart
    Private cartItems As List(Of FormMain.CartItem)
    Private currentUserID As Integer

    Public Sub New(items As List(Of FormMain.CartItem), userID As Integer)
        InitializeComponent()
        cartItems = items
        currentUserID = userID
    End Sub

    Private Sub FormCart_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Keranjang Belanja"
        Me.Size = New Size(800, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(245, 245, 245)

        ' Set form properties untuk ukuran absolute dan tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(800, 600)
        Me.MaximumSize = New Size(800, 600)

        CreateUI()
        LoadCartItems()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Size = New Size(Me.Width, 80),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(52, 152, 219),
            .Dock = DockStyle.Top
        }

        Dim lblTitle As New Label With {
            .Text = "KERANJANG BELANJA",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(30, 25),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)
        Me.Controls.Add(pnlHeader)

        ' Cart Items Panel
        Dim pnlCartItems As New Panel With {
            .Name = "pnlCartItems",
            .Location = New Point(20, 100),
            .Size = New Size(740, 350),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .AutoScroll = True
        }

        Me.Controls.Add(pnlCartItems)

        ' Summary Panel
        Dim pnlSummary As New Panel With {
            .Name = "pnlSummary",
            .Location = New Point(20, 470),
            .Size = New Size(740, 80),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }

        Dim lblTotal As New Label With {
            .Name = "lblTotal",
            .Text = "Total: Rp 0",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(20, 20),
            .AutoSize = True
        }

        Dim btnCheckout As New Button With {
            .Text = "CHECKOUT",
            .Size = New Size(120, 40),
            .Location = New Point(600, 20),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCheckout.FlatAppearance.BorderSize = 0
        AddHandler btnCheckout.Click, AddressOf BtnCheckout_Click

        pnlSummary.Controls.AddRange({lblTotal, btnCheckout})
        Me.Controls.Add(pnlSummary)
    End Sub

    Private Sub LoadCartItems()
        Dim pnlCartItems As Panel = CType(Me.Controls("pnlCartItems"), Panel)
        pnlCartItems.Controls.Clear()

        Dim yPos As Integer = 20
        Dim totalAmount As Decimal = 0

        For Each item In cartItems
            Dim itemPanel As Panel = CreateCartItemPanel(item)
            itemPanel.Location = New Point(20, yPos)
            pnlCartItems.Controls.Add(itemPanel)

            yPos += itemPanel.Height + 10
            totalAmount += item.Total
        Next

        ' Update total
        Dim lblTotal As Label = CType(Me.Controls("pnlSummary").Controls("lblTotal"), Label)
        lblTotal.Text = $"Total: Rp {totalAmount:N0}"
    End Sub

    Private Function CreateCartItemPanel(item As FormMain.CartItem) As Panel
        Dim panel As New Panel With {
            .Size = New Size(680, 80),
            .BackColor = Color.FromArgb(249, 249, 249),
            .BorderStyle = BorderStyle.FixedSingle
        }

        Dim lblProductName As New Label With {
            .Text = item.ProductName,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 15),
            .Size = New Size(300, 25)
        }

        Dim lblPrice As New Label With {
            .Text = $"Rp {item.Price:N0}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 40),
            .AutoSize = True
        }

        Dim numQuantity As New NumericUpDown With {
            .Value = item.Quantity,
            .Minimum = 1,
            .Maximum = 100,
            .Location = New Point(400, 25),
            .Size = New Size(60, 25),
            .Tag = item
        }
        AddHandler numQuantity.ValueChanged, AddressOf NumQuantity_ValueChanged

        Dim lblSubtotal As New Label With {
            .Text = $"Rp {item.Total:N0}",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(500, 25),
            .AutoSize = True
        }

        Dim btnRemove As New Button With {
            .Text = "Hapus",
            .Size = New Size(60, 25),
            .Location = New Point(600, 25),
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand,
            .Tag = item
        }
        btnRemove.FlatAppearance.BorderSize = 0
        AddHandler btnRemove.Click, AddressOf BtnRemove_Click

        panel.Controls.AddRange({lblProductName, lblPrice, numQuantity, lblSubtotal, btnRemove})

        Return panel
    End Function

    Private Sub NumQuantity_ValueChanged(sender As Object, e As EventArgs)
        Dim num As NumericUpDown = CType(sender, NumericUpDown)
        Dim item As FormMain.CartItem = CType(num.Tag, FormMain.CartItem)

        item.Quantity = Convert.ToInt32(num.Value)
        item.Total = item.Price * item.Quantity

        LoadCartItems() ' Refresh display
    End Sub

    Private Sub BtnRemove_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim item As FormMain.CartItem = CType(btn.Tag, FormMain.CartItem)

        If MessageBox.Show($"Hapus {item.ProductName} dari keranjang?", "Konfirmasi",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            cartItems.Remove(item)
            LoadCartItems()
        End If
    End Sub

    Private Sub BtnCheckout_Click(sender As Object, e As EventArgs)
        If cartItems.Count = 0 Then
            MessageBox.Show("Keranjang kosong!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        ' Konfirmasi sebelum ke checkout
        Dim totalAmount As Decimal = cartItems.Sum(Function(item) item.Total)
        Dim confirmMsg As String = $"Checkout Rp {totalAmount:N0}?"

        If MessageBox.Show(confirmMsg, "Konfirmasi",
                          MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            Return
        End If

        Dim frmCheckout As New FormCheckout(cartItems, currentUserID)
        If frmCheckout.ShowDialog() = DialogResult.OK Then
            ' Checkout berhasil - bersihkan keranjang dan kembali ke main
            MessageBox.Show("Checkout selesai! Keranjang dikosongkan.", "Selesai", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Me.DialogResult = DialogResult.OK
            Me.Close()
        End If
    End Sub
End Class