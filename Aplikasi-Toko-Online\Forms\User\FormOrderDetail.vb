Imports MySql.Data.MySqlClient
Imports System.Drawing
Imports System.IO

Public Class FormOrderDetail
    Private orderID As Integer
    
    Public Sub New(orderID As Integer)
        InitializeComponent()
        Me.orderID = orderID
    End Sub
    
    Private Sub FormOrderDetail_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = $"Detail Pesanan #{orderID}"
        Me.Size = New Size(1000, 800)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(245, 245, 245)

        ' Set form properties untuk tidak bisa di-minimize/maximize
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(1000, 800)
        Me.MaximumSize = New Size(1000, 800)

        CreateUI()
        LoadOrderDetail()
    End Sub
    
    Private Sub CreateUI()
        ' Header Panel - POSISI ABSOLUT
        Dim pnlHeader As New Panel With {
            .Size = New Size(<PERSON><PERSON>Width, 80),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(52, 152, 219),
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }
        
        Dim lblTitle As New Label With {
            .Text = $"DETAIL PESANAN #{orderID}",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(30, 25),
            .AutoSize = True
        }
        
        pnlHeader.Controls.Add(lblTitle)

        ' Main Panel with ScrollBar - POSISI ABSOLUT
        Dim pnlMain As New Panel With {
            .Name = "pnlMain",
            .Location = New Point(0, 80),
            .Size = New Size(Me.Width, Me.Height - 80),
            .BackColor = Color.FromArgb(245, 245, 245),
            .AutoScroll = True,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom
        }

        Me.Controls.Add(pnlMain)
        Me.Controls.Add(pnlHeader)
    End Sub
    
    Private Sub LoadOrderDetail()
        Try
            koneksi()
            
            Dim pnlMain As Panel = CType(Me.Controls("pnlMain"), Panel)
            pnlMain.Controls.Clear()
            
            ' Load Order Info
            perintahSQL = New MySqlCommand("SELECT o.*, u.username FROM orders o " &
                                  "JOIN users u ON o.user_id = u.user_id " &
                                  "WHERE o.order_id = @orderID", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@orderID", orderID)
            pembacaData = perintahSQL.ExecuteReader()

            If pembacaData.Read() Then
                Dim orderInfo As Panel = CreateOrderInfoPanel(pembacaData)
                orderInfo.Location = New Point(20, 20)
                pnlMain.Controls.Add(orderInfo)

                pembacaData.Close()

                ' Load Order Items
                Dim itemsPanel As Panel = CreateOrderItemsPanel()
                itemsPanel.Location = New Point(20, 280)
                pnlMain.Controls.Add(itemsPanel)
            Else
                pembacaData.Close()
                MessageBox.Show("Pesanan tidak ditemukan!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Me.Close()
            End If
            
            tutupKoneksi()
            
        Catch ex As Exception
            MessageBox.Show("Error loading order detail: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function CreateOrderInfoPanel(pembacaData As MySqlDataReader) As Panel
        Dim panel As New Panel With {
            .Size = New Size(940, 240),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        ' Order Information
        Dim lblOrderInfo As New Label With {
            .Text = "INFORMASI PESANAN",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblOrderDate As New Label With {
            .Text = $"Tanggal Pesanan: {Convert.ToDateTime(pembacaData("order_date")):dd MMMM yyyy HH:mm}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 45),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblCustomer As New Label With {
            .Text = $"Pelanggan: {pembacaData("username")}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 70),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblStatus As New Label With {
            .Text = $"Status: {GetStatusText(pembacaData("order_status").ToString())}",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = GetStatusColor(pembacaData("order_status").ToString()),
            .Location = New Point(20, 95),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblTotal As New Label With {
            .Text = $"Total Pembayaran: Rp {Convert.ToDecimal(pembacaData("total_amount")):N0}",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(20, 120),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }
        
        ' Shipping Information
        Dim lblShippingInfo As New Label With {
            .Text = "INFORMASI PENGIRIMAN",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(400, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblShippingAddress As New Label With {
            .Text = $"Alamat Pengiriman:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Location = New Point(400, 45),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblAddress As New Label With {
            .Text = pembacaData("shipping_address").ToString(),
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(400, 70),
            .Size = New Size(320, 80),
            .AutoEllipsis = True,
            .BackColor = Color.Transparent
        }

        Dim lblPaymentMethod As New Label With {
            .Text = $"Metode Pembayaran: {If(IsDBNull(pembacaData("payment_method")), "Belum dipilih", pembacaData("payment_method").ToString())}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(400, 160),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }
        
        panel.Controls.AddRange({lblOrderInfo, lblOrderDate, lblCustomer, lblStatus, lblTotal,
                               lblShippingInfo, lblShippingAddress, lblAddress, lblPaymentMethod})
        
        Return panel
    End Function
    
    Private Function CreateOrderItemsPanel() As Panel
        Dim panel As New Panel With {
            .Size = New Size(940, 450),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .AutoScroll = True
        }

        Dim lblItemsTitle As New Label With {
            .Text = "ITEM PESANAN",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 15),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        panel.Controls.Add(lblItemsTitle)

        ' Load order items directly to main panel
        LoadOrderItemsWithImages(panel)

        Return panel
    End Function
    
    Private Sub LoadOrderItemsWithImages(container As Panel)
        Try
            perintahSQL = New MySqlCommand("SELECT od.*, p.product_name, p.image_url FROM order_details od " &
                                  "JOIN products p ON od.product_id = p.product_id " &
                                  "WHERE od.order_id = @orderID", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@orderID", orderID)
            pembacaData = perintahSQL.ExecuteReader()

            Dim yPos As Integer = 50  ' Mulai setelah label "ITEM PESANAN"
            Dim itemCount As Integer = 0

            While pembacaData.Read()
                Dim itemCard As Panel = CreateOrderItemCard(pembacaData)
                itemCard.Location = New Point(20, yPos)
                container.Controls.Add(itemCard)
                yPos += itemCard.Height + 15
                itemCount += 1
            End While

            pembacaData.Close()

            ' Debug: Tampilkan jumlah item yang dimuat
            If itemCount = 0 Then
                Dim lblNoItems As New Label With {
                    .Text = "Tidak ada item ditemukan untuk pesanan ini",
                    .Font = New Font("Segoe UI", 12),
                    .ForeColor = Color.Red,
                    .Location = New Point(20, 50),
                    .AutoSize = True
                }
                container.Controls.Add(lblNoItems)
            End If

        Catch ex As Exception
            MessageBox.Show("Error loading order items: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function CreateOrderItemCard(pembacaData As MySqlDataReader) As Panel
        Dim card As New Panel With {
            .Size = New Size(860, 120),
            .BackColor = Color.FromArgb(249, 249, 249),
            .BorderStyle = BorderStyle.FixedSingle
        }

        ' Product Image
        Dim picProduct As New PictureBox With {
            .Size = New Size(100, 100),
            .Location = New Point(10, 10),
            .SizeMode = PictureBoxSizeMode.Zoom,
            .BackColor = Color.FromArgb(236, 240, 241),
            .BorderStyle = BorderStyle.FixedSingle
        }

        ' Load image
        If Not IsDBNull(pembacaData("image_url")) AndAlso pembacaData("image_url").ToString() <> "" Then
            Try
                If File.Exists(pembacaData("image_url").ToString()) Then
                    picProduct.Image = Image.FromFile(pembacaData("image_url").ToString())
                Else
                    ShowImagePlaceholder(picProduct)
                End If
            Catch
                ShowImagePlaceholder(picProduct)
            End Try
        Else
            ShowImagePlaceholder(picProduct)
        End If

        ' Product Name
        Dim lblProductName As New Label With {
            .Text = pembacaData("product_name").ToString(),
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(130, 15),
            .Size = New Size(300, 25),
            .ForeColor = Color.FromArgb(44, 62, 80),
            .BackColor = Color.Transparent
        }

        ' Price
        Dim price As Decimal = Convert.ToDecimal(pembacaData("price"))
        Dim lblPrice As New Label With {
            .Text = $"Harga: Rp {price:N0}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(130, 45),
            .Size = New Size(200, 20),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .BackColor = Color.Transparent
        }

        ' Quantity
        Dim quantity As Integer = Convert.ToInt32(pembacaData("quantity"))
        Dim lblQuantity As New Label With {
            .Text = $"Jumlah: {quantity}",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(130, 70),
            .Size = New Size(100, 20),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .BackColor = Color.Transparent
        }

        ' Subtotal
        Dim subtotal As Decimal
        If Not IsDBNull(pembacaData("subtotal")) Then
            subtotal = Convert.ToDecimal(pembacaData("subtotal"))
        Else
            subtotal = price * quantity
        End If

        Dim lblSubtotal As New Label With {
            .Text = $"Subtotal: Rp {subtotal:N0}",
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Location = New Point(600, 45),
            .Size = New Size(200, 25),
            .ForeColor = Color.FromArgb(231, 76, 60),
            .BackColor = Color.Transparent,
            .TextAlign = ContentAlignment.MiddleRight
        }

        card.Controls.AddRange({picProduct, lblProductName, lblPrice, lblQuantity, lblSubtotal})

        Return card
    End Function

    Private Sub ShowImagePlaceholder(picBox As PictureBox)
        ' Create placeholder label inside PictureBox
        Dim lblPlaceholder As New Label With {
            .Text = "IMG",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(189, 195, 199),
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter,
            .BackColor = Color.Transparent
        }
        picBox.Controls.Add(lblPlaceholder)
    End Sub

    Private Function GetStatusText(status As String) As String
        Select Case status.ToLower()
            Case "pending"
                Return "Menunggu Konfirmasi"
            Case "processing"
                Return "Sedang Diproses"
            Case "shipped"
                Return "Sedang Dikirim"
            Case "success"
                Return "Berhasil Selesai"
            Case "cancelled"
                Return "Dibatalkan"
            Case Else
                Return status
        End Select
    End Function
    
    Private Function GetStatusColor(status As String) As Color
        Select Case status.ToLower()
            Case "pending"
                Return Color.FromArgb(241, 196, 15)
            Case "processing"
                Return Color.FromArgb(52, 152, 219)
            Case "shipped"
                Return Color.FromArgb(155, 89, 182)
            Case "success"
                Return Color.FromArgb(46, 204, 113)
            Case "cancelled"
                Return Color.FromArgb(231, 76, 60)
            Case Else
                Return Color.Gray
        End Select
    End Function
End Class
