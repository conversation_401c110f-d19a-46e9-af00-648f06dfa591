# Database Directory

Folder ini berisi semua file SQL dan script database untuk aplikasi toko online.

## 📁 File Database

### 🏗️ Schema & Structure
- `database_orders.sql` - Script pembuatan tabel orders dan order_details
- `database_improvements.sql` - Perbaikan dan optimasi database
- `check_database_structure.sql` - Script untuk mengecek struktur database

### 🔧 Maintenance & Updates
- `add_role_column.sql` - Men<PERSON>bah kolom role untuk user
- `fix_updated_at_column.sql` - Perbaikan kolom updated_at
- `update_status_to_success.sql` - Update status 'delivered' ke 'success'

### 🧪 Testing
- `test_insert_order.sql` - Script untuk testing insert data order

## 🎯 Cara Penggunaan

1. **Setup Database Baru:**
   ```sql
   -- Jalankan script ini secara berurutan:
   1. database_orders.sql
   2. add_role_column.sql
   3. database_improvements.sql
   ```

2. **Update Database yang Ada:**
   ```sql
   -- Untuk update database existing:
   1. check_database_structure.sql (cek dulu)
   2. fix_updated_at_column.sql
   3. update_status_to_success.sql
   ```

3. **Testing:**
   ```sql
   -- Untuk testing:
   test_insert_order.sql
   ```

## 📊 Database Schema

### Tabel Utama:
- `users` - Data pengguna
- `categories` - Kategori produk
- `products` - Data produk
- `orders` - Data pesanan
- `order_details` - Detail item pesanan
- `wishlist` - Daftar keinginan user

### Status Order:
- `pending` - Menunggu konfirmasi
- `processing` - Sedang diproses
- `shipped` - Sedang dikirim
- `success` - Berhasil selesai
- `cancelled` - Dibatalkan

## ⚠️ Catatan Penting

- Selalu backup database sebelum menjalankan script update
- Jalankan script di environment testing terlebih dahulu
- Periksa dependency antar tabel sebelum melakukan perubahan struktur
