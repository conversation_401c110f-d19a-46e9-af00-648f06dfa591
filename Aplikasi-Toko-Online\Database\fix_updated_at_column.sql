-- <PERSON><PERSON>t untuk menambahkan kolom updated_at jika belum ada
-- Jalankan script ini di MySQL untuk memastikan kolom updated_at ada

USE db_ecommerce;

-- <PERSON><PERSON> apa<PERSON>h kolom updated_at sudah ada
-- Jika belum ada, tambahkan kolom updated_at
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Verifikasi struktur tabel
DESCRIBE orders;

-- Update existing records to have proper updated_at values
UPDATE orders SET updated_at = created_at WHERE updated_at IS NULL;
