-- Script untuk memeriksa struktur database yang ada
USE db_ecommerce;

-- Ce<PERSON> struktur tabel orders
DESCRIBE orders;

-- Cek struktur tabel order_details  
DESCRIBE order_details;

-- Cek struktur tabel users (untuk memastikan kolom address ada)
DESCRIBE users;

-- Cek struktur tabel products
DESCRIBE products;

-- Cek data sample di tabel orders (jika ada)
SELECT * FROM orders LIMIT 5;

-- Cek data sample di tabel order_details (jika ada)
SELECT * FROM order_details LIMIT 5;

-- Cek apakah ada constraint atau foreign key
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'db_ecommerce' 
AND (TABLE_NAME = 'orders' OR TABLE_NAME = 'order_details');
