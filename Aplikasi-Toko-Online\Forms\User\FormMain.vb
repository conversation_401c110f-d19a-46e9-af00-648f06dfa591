' FormMain.vb - Form Utama Aplikasi E-Commerce (Complete Updated Version)
Imports MySql.Data.MySqlClient
Imports System.IO

Public Class FormMain
    ' User properties
    Public Property currentUserID As Integer = 1
    Public Property currentUserName As String = "Guest"
    Public Property isAdmin As Boolean = False

    ' Cart items
    Private cartItems As New List(Of CartItem)

    ' Wishlist items
    Private wishlistItems As New List(Of Integer)

    ' Timer untuk promotional banner
    Private WithEvents bannerTimer As New Timer()
    Private currentBannerIndex As Integer = 0
    Private bannerMessages As String() = {
        "PROMO: Diskon 20% untuk pembelian diatas Rp 500.000!",
        "GRATIS: Ongkir untuk pembelian pertama!",
        "FLASH SALE: Setiap hari jam 12:00 - 14:00!",
        "CICILAN: 0% untuk kartu kredit tertentu!"
    }

    ' Class untuk item di keranjang
    Public Class CartItem
        Public Property ProductID As Integer
        Public Property ProductName As String
        Public Property Price As Decimal
        Public Property Quantity As Integer
        Public Property Total As Decimal
    End Class

    Private Sub FormMain_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ' Setup form
        Me.Text = "Toko Online - " & currentUserName
        Me.WindowState = FormWindowState.Maximized
        Me.BackColor = Color.FromArgb(245, 245, 245)

        ' Setup banner timer
        bannerTimer.Interval = 5000 ' 5 seconds
        bannerTimer.Start()

        ' Create UI
        CreateUI()

        ' Load initial data
        LoadCategories()
        LoadProducts()
        LoadWishlist()

        ' Update user info
        UpdateUserInfo()

        ' Initialize auto order status manager
        Dim statusManager As OrderStatusManager = OrderStatusManager.Instance
    End Sub

    Private Sub CreateUI()
        ' Panel Header dengan gradient effect
        Dim pnlHeader As New Panel With {
            .Name = "pnlHeader",
            .Dock = DockStyle.Top,
            .Height = 100
        }

        ' Gradient background for header
        AddHandler pnlHeader.Paint, Sub(sender, e)
                                        Using brush As New Drawing2D.LinearGradientBrush(
                                            pnlHeader.ClientRectangle,
                                            Color.FromArgb(41, 128, 185),
                                            Color.FromArgb(52, 152, 219),
                                            Drawing2D.LinearGradientMode.Horizontal)
                                            e.Graphics.FillRectangle(brush, pnlHeader.ClientRectangle)
                                        End Using
                                    End Sub

        ' Logo and Title
        Dim lblTitle As New Label With {
            .Text = "TOKO ONLINE",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.White,
            .BackColor = Color.Transparent,
            .Location = New Point(10, 10),
            .AutoSize = True
        }

        Dim lblSubtitle As New Label With {
            .Text = "Belanja Mudah, Harga Terjangkau",
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.White,
            .BackColor = Color.Transparent,
            .Location = New Point(20, 60),
            .AutoSize = True
        }

        ' Panel untuk user info dan controls di kanan
        Dim pnlHeaderRight As New Panel With {
            .Size = New Size(800, 80),
            .Location = New Point(Me.Width - 850, 10),
            .BackColor = Color.Transparent
        }

        ' User info panel
        Dim pnlUserInfo As New Panel With {
            .Size = New Size(200, 30),
            .Location = New Point(0, 5),
            .BackColor = Color.FromArgb(30, 0, 0, 0)
        }

        Dim lblUserIcon As New Label With {
            .Text = "USER:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(5, 5),
            .AutoSize = True
        }

        Dim lblUserName As New Label With {
            .Name = "lblUserName",
            .Text = currentUserName,
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.White,
            .Location = New Point(55, 5),
            .AutoSize = True
        }

        pnlUserInfo.Controls.AddRange({lblUserIcon, lblUserName})

        ' Search Panel
        Dim pnlSearch As New Panel With {
            .Size = New Size(300, 35),
            .Location = New Point(0, 40),
            .BackColor = Color.White
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(230, 30),
            .Location = New Point(5, 8),
            .Font = New Font("Segoe UI", 10),
            .BorderStyle = BorderStyle.None,
            .ForeColor = Color.FromArgb(52, 73, 94)
        }
        ' PlaceholderText alternative for older versions
        AddHandler txtSearch.Enter, Sub()
                                        If txtSearch.Text = "Cari produk..." Then
                                            txtSearch.Text = ""
                                            txtSearch.ForeColor = Color.Black
                                        End If
                                    End Sub

        AddHandler txtSearch.Leave, Sub()
                                        If txtSearch.Text = "" Then
                                            txtSearch.Text = "Cari produk..."
                                            txtSearch.ForeColor = Color.Gray
                                        End If
                                    End Sub

        ' Set initial placeholder
        txtSearch.Text = "Cari produk..."
        txtSearch.ForeColor = Color.Gray

        Dim btnSearch As New Button With {
            .Text = "Search",
            .Size = New Size(60, 30),
            .Location = New Point(235, 2),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnSearch.FlatAppearance.BorderSize = 0
        AddHandler btnSearch.Click, AddressOf BtnSearch_Click

        pnlSearch.Controls.AddRange({txtSearch, btnSearch})

        ' Action buttons
        Dim btnWishlist As New Button With {
            .Text = "WISH (0)",
            .Name = "btnWishlist",
            .Size = New Size(85, 35),
            .Location = New Point(310, 40),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnWishlist.FlatAppearance.BorderSize = 0
        AddHandler btnWishlist.Click, AddressOf BtnWishlist_Click

        Dim btnCart As New Button With {
            .Text = "CART (0)",
            .Name = "btnCart",
            .Size = New Size(85, 35),
            .Location = New Point(400, 40),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnCart.FlatAppearance.BorderSize = 0
        AddHandler btnCart.Click, AddressOf BtnCart_Click

        Dim btnOrders As New Button With {
            .Text = "PESANAN",
            .Name = "btnOrders",
            .Size = New Size(75, 35),
            .Location = New Point(490, 40),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(155, 89, 182),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 8),
            .Cursor = Cursors.Hand
        }
        btnOrders.FlatAppearance.BorderSize = 0
        AddHandler btnOrders.Click, AddressOf BtnOrders_Click





        ' Menu button
        Dim btnMenu As New Button With {
            .Text = "MENU",
            .Name = "btnMenu",
            .Size = New Size(65, 35),
            .Location = New Point(570, 40),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.FromArgb(127, 140, 141),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnMenu.FlatAppearance.BorderSize = 0
        AddHandler btnMenu.Click, AddressOf BtnMenu_Click

        pnlHeaderRight.Controls.AddRange({pnlUserInfo, pnlSearch, btnWishlist, btnCart, btnOrders, btnMenu})
        pnlHeader.Controls.AddRange({lblTitle, lblSubtitle, pnlHeaderRight})

        ' Promotional Banner
        Dim pnlBanner As New Panel With {
            .Name = "pnlBanner",
            .Dock = DockStyle.Top,
            .Height = 40,
            .BackColor = Color.FromArgb(255, 193, 7)
        }

        Dim lblBanner As New Label With {
            .Name = "lblBanner",
            .Text = bannerMessages(0),
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter
        }

        pnlBanner.Controls.Add(lblBanner)

        ' Panel Sidebar untuk kategori dengan modern design
        Dim pnlSidebar As New Panel With {
            .Name = "pnlSidebar",
            .Dock = DockStyle.Left,
            .Width = 250,
            .BackColor = Color.White
        }

        ' Add shadow effect to sidebar
        AddHandler pnlSidebar.Paint, Sub(sender, e)
                                         Dim rect As Rectangle = New Rectangle(pnlSidebar.Width - 5, 0, 5, pnlSidebar.Height)
                                         Using brush As New Drawing2D.LinearGradientBrush(
                                             rect,
                                             Color.FromArgb(50, 0, 0, 0),
                                             Color.Transparent,
                                             Drawing2D.LinearGradientMode.Horizontal)
                                             e.Graphics.FillRectangle(brush, rect)
                                         End Using
                                     End Sub

        ' Category header
        Dim pnlCategoryHeader As New Panel With {
            .Size = New Size(250, 50),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(236, 240, 241)
        }

        Dim lblKategori As New Label With {
            .Text = "KATEGORI PRODUK",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 15),
            .AutoSize = True,
            .ForeColor = Color.FromArgb(52, 73, 94)
        }

        pnlCategoryHeader.Controls.Add(lblKategori)
        pnlSidebar.Controls.Add(pnlCategoryHeader)

        ' Main content area
        Dim pnlContent As New Panel With {
            .Name = "pnlContent",
            .Dock = DockStyle.Fill,
            .BackColor = Color.FromArgb(245, 245, 245)
        }

        ' Sorting panel
        Dim pnlSort As New Panel With {
            .Name = "pnlSort",
            .Dock = DockStyle.Top,
            .Height = 50,
            .BackColor = Color.White,
            .Padding = New Padding(20, 10, 20, 10)
        }

        Dim lblSort As New Label With {
            .Text = "Urutkan:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 15),
            .AutoSize = True
        }

        Dim cmbSort As New ComboBox With {
            .Name = "cmbSort",
            .Size = New Size(200, 25),
            .Location = New Point(90, 12),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 10)
        }
        cmbSort.Items.AddRange({"Terbaru", "Harga Terendah", "Harga Tertinggi", "Nama A-Z", "Nama Z-A", "Terlaris"})
        cmbSort.SelectedIndex = 0
        AddHandler cmbSort.SelectedIndexChanged, AddressOf CmbSort_SelectedIndexChanged

        Dim lblProductCount As New Label With {
            .Name = "lblProductCount",
            .Text = "Menampilkan 0 produk",
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(Me.Width - 450, 15),
            .AutoSize = True
        }

        pnlSort.Controls.AddRange({lblSort, cmbSort, lblProductCount})

        ' Panel untuk produk dengan custom scrollbar
        Dim pnlProducts As New FlowLayoutPanel With {
            .Name = "pnlProducts",
            .Dock = DockStyle.Fill,
            .AutoScroll = True,
            .BackColor = Color.FromArgb(245, 245, 245),
            .Padding = New Padding(15)
        }

        pnlContent.Controls.AddRange({pnlProducts, pnlSort})

        ' Add all panels to form
        Me.Controls.AddRange({pnlContent, pnlSidebar, pnlBanner, pnlHeader})

        ' TAMBAHKAN FOOTER
        Dim pnlFooter As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 40,
            .BackColor = Color.FromArgb(44, 62, 80)
        }

        Dim lblCopyright As New Label With {
            .Text = "© 2024 Toko Online - Developed with Love | Support: 0812-3456-7890",
            .ForeColor = Color.White,
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter,
            .Font = New Font("Segoe UI", 9)
        }

        pnlFooter.Controls.Add(lblCopyright)
        Me.Controls.Add(pnlFooter)
    End Sub

    Private Sub LoadCategories()
        Try
            koneksi()

            Dim pnlSidebar As Panel = CType(Me.Controls("pnlSidebar"), Panel)

            ' Clear existing category buttons
            Dim controlsToRemove As New List(Of Control)
            For Each ctrl In pnlSidebar.Controls
                If TypeOf ctrl Is Button AndAlso ctrl.Name.StartsWith("btnCat_") Then
                    controlsToRemove.Add(ctrl)
                End If
            Next
            For Each ctrl In controlsToRemove
                pnlSidebar.Controls.Remove(ctrl)
            Next

            ' Add "All Products" button
            Dim btnAll As New Button With {
                .Text = "ALL - Semua Produk",
                .Name = "btnCat_0",
                .Tag = 0,
                .Size = New Size(220, 45),
                .Location = New Point(15, 60),
                .FlatStyle = FlatStyle.Flat,
                .BackColor = Color.FromArgb(52, 152, 219),
                .ForeColor = Color.White,
                .TextAlign = ContentAlignment.MiddleLeft,
                .Font = New Font("Segoe UI", 10),
                .Cursor = Cursors.Hand,
                .Padding = New Padding(10, 0, 0, 0)
            }
            btnAll.FlatAppearance.BorderSize = 0
            AddHandler btnAll.Click, AddressOf CategoryButton_Click
            AddHandler btnAll.MouseEnter, Sub(s, e)
                                              If btnAll.BackColor <> Color.FromArgb(52, 152, 219) Then
                                                  btnAll.BackColor = Color.FromArgb(236, 240, 241)
                                              End If
                                          End Sub
            AddHandler btnAll.MouseLeave, Sub(s, e)
                                              If btnAll.BackColor <> Color.FromArgb(52, 152, 219) Then
                                                  btnAll.BackColor = Color.White
                                              End If
                                          End Sub

            pnlSidebar.Controls.Add(btnAll)

            ' Load categories from database
            perintahSQL = New MySqlCommand("SELECT * FROM categories ORDER BY category_name", koneksiDatabase)
            pembacaData = perintahSQL.ExecuteReader()

            Dim yPos As Integer = 110
            Dim categoryIcons As New Dictionary(Of String, String) From {
                {"Elektronik", "ELEC"},
                {"Fashion", "FASH"},
                {"Makanan", "FOOD"},
                {"Buku", "BOOK"}
            }

            While pembacaData.Read()
                Dim categoryName As String = pembacaData("category_name").ToString()
                Dim icon As String = If(categoryIcons.ContainsKey(categoryName), categoryIcons(categoryName), "CAT")

                Dim btnCategory As New Button With {
                    .Text = icon & " " & categoryName,
                    .Name = "btnCat_" & pembacaData("category_id").ToString(),
                    .Tag = pembacaData("category_id"),
                    .Size = New Size(220, 45),
                    .Location = New Point(15, yPos),
                    .FlatStyle = FlatStyle.Flat,
                    .BackColor = Color.White,
                    .ForeColor = Color.FromArgb(52, 73, 94),
                    .TextAlign = ContentAlignment.MiddleLeft,
                    .Font = New Font("Segoe UI", 10),
                    .Cursor = Cursors.Hand,
                    .Padding = New Padding(10, 0, 0, 0)
                }
                btnCategory.FlatAppearance.BorderSize = 0

                AddHandler btnCategory.Click, AddressOf CategoryButton_Click
                AddHandler btnCategory.MouseEnter, Sub(s, e)
                                                       If btnCategory.BackColor <> Color.FromArgb(52, 152, 219) Then
                                                           btnCategory.BackColor = Color.FromArgb(236, 240, 241)
                                                       End If
                                                   End Sub
                AddHandler btnCategory.MouseLeave, Sub(s, e)
                                                       If btnCategory.BackColor <> Color.FromArgb(52, 152, 219) Then
                                                           btnCategory.BackColor = Color.White
                                                       End If
                                                   End Sub

                pnlSidebar.Controls.Add(btnCategory)
                yPos += 50
            End While

            pembacaData.Close()
            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading categories: " & ex.Message)
        End Try
    End Sub

    Private Sub LoadProducts(Optional categoryID As Integer = 0, Optional searchText As String = "", Optional sortBy As String = "Terbaru")
        ' Show loading first
        Dim pnlProducts As FlowLayoutPanel = CType(Me.Controls.Find("pnlProducts", True).FirstOrDefault(), FlowLayoutPanel)
        pnlProducts.Controls.Clear()

        ' Loading indicator
        Dim pnlLoading As New Panel With {
            .Size = New Size(400, 200),
            .BackColor = Color.Transparent
        }

        Dim pbLoading As New ProgressBar With {
            .Style = ProgressBarStyle.Marquee,
            .Size = New Size(200, 30),
            .Location = New Point(100, 70)
        }

        Dim lblLoading As New Label With {
            .Text = "Loading products...",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Size = New Size(400, 30),
            .Location = New Point(0, 110),
            .TextAlign = ContentAlignment.MiddleCenter
        }

        pnlLoading.Controls.AddRange({pbLoading, lblLoading})
        pnlProducts.Controls.Add(pnlLoading)
        Application.DoEvents() ' Force UI update

        Try
            koneksi()

            Dim query As String = "SELECT p.*, c.category_name, " &
                                 "(SELECT COUNT(*) FROM wishlist WHERE product_id = p.product_id) as wishlist_count, " &
                                 "(SELECT COUNT(*) FROM order_details od JOIN orders o ON od.order_id = o.order_id WHERE od.product_id = p.product_id) as sold_count " &
                                 "FROM products p " &
                                 "JOIN categories c ON p.category_id = c.category_id WHERE 1=1"

            If categoryID > 0 Then
                query &= " AND p.category_id = " & categoryID
            End If

            If searchText <> "" Then
                query &= " AND (p.product_name LIKE '%" & searchText & "%' OR p.description LIKE '%" & searchText & "%')"
            End If

            ' Sorting
            Select Case sortBy
                Case "Harga Terendah"
                    query &= " ORDER BY p.price ASC"
                Case "Harga Tertinggi"
                    query &= " ORDER BY p.price DESC"
                Case "Nama A-Z"
                    query &= " ORDER BY p.product_name ASC"
                Case "Nama Z-A"
                    query &= " ORDER BY p.product_name DESC"
                Case "Terlaris"
                    query &= " ORDER BY sold_count DESC"
                Case Else
                    query &= " ORDER BY p.product_id DESC"
            End Select

            perintahSQL = New MySqlCommand(query, koneksiDatabase)
            pembacaData = perintahSQL.ExecuteReader()

            ' Remove loading indicator
            pnlProducts.Controls.Remove(pnlLoading)

            Dim productCount As Integer = 0

            While pembacaData.Read()
                Dim product As New Dictionary(Of String, Object) From {
                    {"product_id", pembacaData("product_id")},
                    {"product_name", pembacaData("product_name").ToString()},
                    {"category_name", pembacaData("category_name").ToString()},
                    {"price", Convert.ToDecimal(pembacaData("price"))},
                    {"stock", Convert.ToInt32(pembacaData("stock"))},
                    {"description", pembacaData("description").ToString()},
                    {"image_url", If(IsDBNull(pembacaData("image_url")), "", pembacaData("image_url").ToString())},
                    {"wishlist_count", Convert.ToInt32(pembacaData("wishlist_count"))},
                    {"sold_count", Convert.ToInt32(pembacaData("sold_count"))}
                }
                pnlProducts.Controls.Add(CreateProductCard(product))
                productCount += 1
            End While

            pembacaData.Close()
            tutupKoneksi()

            ' Update product count label
            Dim lblProductCount As Label = CType(Me.Controls.Find("lblProductCount", True).FirstOrDefault(), Label)
            If lblProductCount IsNot Nothing Then
                lblProductCount.Text = $"Menampilkan {productCount} produk"
            End If

            If pnlProducts.Controls.Count = 0 Then
                Dim pnlEmpty As New Panel With {
                    .Size = New Size(400, 200)
                }

                Dim lblNoProduct As New Label With {
                    .Text = ":(",
                    .Font = New Font("Segoe UI", 48),
                    .ForeColor = Color.FromArgb(189, 195, 199),
                    .Size = New Size(400, 80),
                    .TextAlign = ContentAlignment.MiddleCenter
                }

                Dim lblNoProductText As New Label With {
                    .Text = "Tidak ada produk ditemukan",
                    .Font = New Font("Segoe UI", 14),
                    .ForeColor = Color.FromArgb(127, 140, 141),
                    .Location = New Point(0, 80),
                    .Size = New Size(400, 30),
                    .TextAlign = ContentAlignment.MiddleCenter
                }

                pnlEmpty.Controls.AddRange({lblNoProduct, lblNoProductText})
                pnlProducts.Controls.Add(pnlEmpty)
            End If

        Catch ex As Exception
            ' Remove loading on error
            pnlProducts.Controls.Remove(pnlLoading)
            MessageBox.Show("Error loading products: " & ex.Message)
        End Try
    End Sub

    Private Function CreateProductCard(product As Dictionary(Of String, Object)) As Panel
        Dim card As New Panel With {
            .Size = New Size(330, 480),
            .BackColor = Color.White,
            .Margin = New Padding(10),
            .Cursor = Cursors.Hand
        }

        ' TAMBAHKAN DOUBLE CLICK HANDLER
        AddHandler card.DoubleClick, Sub()
                                         ShowProductQuickView(product)
                                     End Sub

        ' Card hover effect
        AddHandler card.MouseEnter, Sub(s, e)
                                        card.BackColor = Color.FromArgb(250, 250, 250)
                                        ' Add shadow
                                        card.Padding = New Padding(2)
                                    End Sub

        AddHandler card.MouseLeave, Sub(s, e)
                                        card.BackColor = Color.White
                                        card.Padding = New Padding(0)
                                    End Sub

        ' Shadow effect
        AddHandler card.Paint, Sub(sender, e)
                                   Dim rect As Rectangle = New Rectangle(3, 3, card.Width - 6, card.Height - 6)
                                   Using brush As New SolidBrush(Color.FromArgb(30, 0, 0, 0))
                                       e.Graphics.FillRectangle(brush, rect)
                                   End Using
                               End Sub

        ' Panel untuk gambar UPDATED
        Dim pnlImage As New Panel With {
            .Size = New Size(330, 240),
            .Location = New Point(0, 0),
            .BackColor = Color.FromArgb(236, 240, 241)
        }

        ' PictureBox untuk gambar produk
        Dim picProduct As New PictureBox With {
            .Name = "picProduct",
            .Size = New Size(330, 240),
            .Location = New Point(0, 0),
            .SizeMode = PictureBoxSizeMode.Zoom,
            .BackColor = Color.FromArgb(236, 240, 241)
        }

        ' Load gambar dari database atau file
        If product("image_url").ToString() <> "" Then
            Try
                ' Coba load dari file path atau URL
                If File.Exists(product("image_url").ToString()) Then
                    picProduct.Image = Image.FromFile(product("image_url").ToString())
                Else
                    ' Jika file tidak ada, tampilkan placeholder
                    ShowImagePlaceholder(picProduct)
                End If
            Catch
                ' Jika error, tampilkan placeholder
                ShowImagePlaceholder(picProduct)
            End Try
        Else
            ' Jika tidak ada URL gambar
            ShowImagePlaceholder(picProduct)
        End If

        pnlImage.Controls.Add(picProduct)

        ' Wishlist button - SUPER VISIBLE WITH BORDER
        Dim isInWishlist As Boolean = wishlistItems.Contains(Convert.ToInt32(product("product_id")))
        Dim btnWishlist As New Label With {
            .Text = If(isInWishlist, "♥", "♡"),
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .Size = New Size(50, 50),
            .Location = New Point(270, 8),
            .BackColor = Color.White,
            .ForeColor = If(isInWishlist, Color.FromArgb(231, 76, 60), Color.FromArgb(149, 165, 166)),
            .TextAlign = ContentAlignment.MiddleCenter,
            .Cursor = Cursors.Hand,
            .Tag = product("product_id"),
            .BorderStyle = BorderStyle.FixedSingle,
            .Padding = New Padding(2, 0, 0, 0)
        }

        ' Make wishlist button circular with smaller size
        Dim wishPath As New Drawing2D.GraphicsPath()
        Dim circleSize As Integer = 35  ' Ukuran lingkaran lebih kecil dari 50x50
        Dim offsetX As Integer = (btnWishlist.Width - circleSize) \ 2
        Dim offsetY As Integer = (btnWishlist.Height - circleSize) \ 2
        wishPath.AddEllipse(offsetX, offsetY, circleSize, circleSize)
        btnWishlist.Region = New Region(wishPath)

        ' Add hover effects for wishlist button
        AddHandler btnWishlist.MouseEnter, Sub(wishBtn, mouseEnterEvent)
                                               Dim currentBtn As Label = CType(wishBtn, Label)
                                               Dim currentProductID As Integer = Convert.ToInt32(currentBtn.Tag)
                                               Dim currentIsInWishlist As Boolean = wishlistItems.Contains(currentProductID)
                                               currentBtn.Font = New Font("Segoe UI", 20, FontStyle.Bold)
                                               currentBtn.ForeColor = If(currentIsInWishlist, Color.FromArgb(192, 57, 43), Color.FromArgb(231, 76, 60))
                                           End Sub

        AddHandler btnWishlist.MouseLeave, Sub(wishBtn, mouseLeaveEvent)
                                               Dim currentBtn As Label = CType(wishBtn, Label)
                                               Dim currentProductID As Integer = Convert.ToInt32(currentBtn.Tag)
                                               Dim currentIsInWishlist As Boolean = wishlistItems.Contains(currentProductID)
                                               currentBtn.Font = New Font("Segoe UI", 18, FontStyle.Bold)
                                               currentBtn.ForeColor = If(currentIsInWishlist, Color.FromArgb(231, 76, 60), Color.FromArgb(149, 165, 166))
                                           End Sub

        AddHandler btnWishlist.Click, AddressOf BtnProductWishlist_Click

        ' Sale badge if applicable - TAMBAHKAN DULU
        If Convert.ToInt32(product("sold_count")) > 10 Then
            Dim lblBadge As New Label With {
                .Text = "BEST SELLER",
                .Font = New Font("Segoe UI", 8, FontStyle.Bold),
                .BackColor = Color.FromArgb(231, 76, 60),
                .ForeColor = Color.White,
                .Size = New Size(80, 20),
                .Location = New Point(10, 10),
                .TextAlign = ContentAlignment.MiddleCenter
            }
            pnlImage.Controls.Add(lblBadge)
        End If

        ' TAMBAHKAN WISHLIST BUTTON TERAKHIR AGAR DI ATAS SEMUA
        pnlImage.Controls.Add(btnWishlist)
        btnWishlist.BringToFront()

        ' Product info panel
        Dim pnlInfo As New Panel With {
            .Size = New Size(310, 210),
            .Location = New Point(10, 250),
            .BackColor = Color.Transparent
        }

        ' Product name
        Dim lblName As New Label With {
            .Text = product("product_name").ToString(),
            .Location = New Point(0, 0),
            .Size = New Size(310, 45),
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }

        ' Category
        Dim lblCategory As New Label With {
            .Text = product("category_name").ToString(),
            .Location = New Point(0, 45),
            .Size = New Size(310, 22),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Font = New Font("Segoe UI", 10)
        }

        ' Rating stars (dummy)
        Dim lblRating As New Label With {
            .Text = "★★★★☆ (4.5)",
            .Location = New Point(0, 67),
            .Size = New Size(140, 22),
            .Font = New Font("Segoe UI", 10)
        }

        ' Sold count
        Dim lblSold As New Label With {
            .Text = $"Terjual {product("sold_count")}",
            .Location = New Point(150, 67),
            .Size = New Size(150, 22),
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .TextAlign = ContentAlignment.TopRight
        }

        ' Price
        Dim lblPrice As New Label With {
            .Text = "Rp " & Convert.ToDecimal(product("price")).ToString("N0"),
            .Location = New Point(0, 95),
            .Size = New Size(310, 35),
            .Font = New Font("Segoe UI", 18, FontStyle.Bold),
            .ForeColor = Color.FromArgb(231, 76, 60)
        }

        ' Stock status
        Dim stockStatus As String = ""
        Dim stockColor As Color = Color.Black

        If Convert.ToInt32(product("stock")) = 0 Then
            stockStatus = "Habis"
            stockColor = Color.FromArgb(231, 76, 60)
        ElseIf Convert.ToInt32(product("stock")) < 10 Then
            stockStatus = $"Tersisa {product("stock")}"
            stockColor = Color.FromArgb(230, 126, 34)
        Else
            stockStatus = "Tersedia"
            stockColor = Color.FromArgb(39, 174, 96)
        End If

        Dim lblStock As New Label With {
            .Text = stockStatus,
            .Location = New Point(0, 135),
            .Size = New Size(120, 22),
            .ForeColor = stockColor,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold)
        }

        ' Add to cart button
        Dim btnBuy As New Button With {
            .Text = If(Convert.ToInt32(product("stock")) > 0, "TAMBAH KE KERANJANG", "Stok Habis"),
            .Tag = product,
            .Location = New Point(0, 165),
            .Size = New Size(150, 35),
            .BackColor = If(Convert.ToInt32(product("stock")) > 0, Color.FromArgb(52, 152, 219), Color.Gray),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 8, FontStyle.Bold),
            .Enabled = Convert.ToInt32(product("stock")) > 0,
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter,
            .Margin = New Padding(0, 0, 0, 10)
        }
        btnBuy.FlatAppearance.BorderSize = 0
        AddHandler btnBuy.Click, AddressOf BtnBuy_Click

        ' Buy now button
        Dim btnBuyNow As New Button With {
            .Text = "BELI SEKARANG",
            .Tag = product,
            .Location = New Point(155, 165),
            .Size = New Size(150, 35),
            .BackColor = If(Convert.ToInt32(product("stock")) > 0, Color.FromArgb(46, 204, 113), Color.Gray),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 8, FontStyle.Bold),
            .Enabled = Convert.ToInt32(product("stock")) > 0,
            .Cursor = Cursors.Hand,
            .TextAlign = ContentAlignment.MiddleCenter,
            .Margin = New Padding(0, 0, 0, 10)
        }
        btnBuyNow.FlatAppearance.BorderSize = 0
        AddHandler btnBuyNow.Click, AddressOf BtnBuyNow_Click

        pnlInfo.Controls.AddRange({lblName, lblCategory, lblRating, lblSold, lblPrice, lblStock, btnBuy, btnBuyNow})
        card.Controls.AddRange({pnlImage, pnlInfo})

        Return card
    End Function

    Private Sub ShowImagePlaceholder(picBox As PictureBox)
        ' Create placeholder label inside PictureBox
        Dim lblPlaceholder As New Label With {
            .Text = "IMG",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(189, 195, 199),
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter,
            .BackColor = Color.Transparent
        }
        picBox.Controls.Add(lblPlaceholder)
    End Sub

    Private Sub ShowProductQuickView(product As Dictionary(Of String, Object))
        Dim quickView As New Form With {
            .Size = New Size(700, 500),
            .StartPosition = FormStartPosition.CenterParent,
            .Text = product("product_name").ToString(),
            .FormBorderStyle = FormBorderStyle.FixedDialog,
            .MaximizeBox = False,
            .MinimizeBox = False,
            .BackColor = Color.White
        }

        ' Left panel for image
        Dim pnlLeft As New Panel With {
            .Size = New Size(300, 500),
            .Dock = DockStyle.Left,
            .BackColor = Color.FromArgb(245, 245, 245)
        }

        Dim picProduct As New PictureBox With {
            .Size = New Size(250, 250),
            .Location = New Point(25, 25),
            .BackColor = Color.White,
            .SizeMode = PictureBoxSizeMode.Zoom
        }

        ' Load image for quick view
        If product("image_url").ToString() <> "" AndAlso File.Exists(product("image_url").ToString()) Then
            Try
                picProduct.Image = Image.FromFile(product("image_url").ToString())
            Catch
                ShowImagePlaceholder(picProduct)
            End Try
        Else
            ShowImagePlaceholder(picProduct)
        End If

        pnlLeft.Controls.Add(picProduct)

        ' Right panel for details
        Dim pnlRight As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        Dim lblName As New Label With {
            .Text = product("product_name").ToString(),
            .Font = New Font("Segoe UI", 18, FontStyle.Bold),
            .Location = New Point(20, 20),
            .Size = New Size(340, 40),
            .ForeColor = Color.FromArgb(44, 62, 80)
        }

        Dim lblCategory As New Label With {
            .Text = "Kategori: " & product("category_name").ToString(),
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 65),
            .Size = New Size(340, 25),
            .ForeColor = Color.FromArgb(127, 140, 141)
        }

        Dim lblPrice As New Label With {
            .Text = "Rp " & Convert.ToDecimal(product("price")).ToString("N0"),
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.FromArgb(231, 76, 60),
            .Location = New Point(20, 100),
            .Size = New Size(340, 40)
        }

        Dim lblDescTitle As New Label With {
            .Text = "Deskripsi:",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Location = New Point(20, 150),
            .Size = New Size(340, 25)
        }

        Dim txtDesc As New TextBox With {
            .Text = product("description").ToString(),
            .Location = New Point(20, 180),
            .Size = New Size(340, 100),
            .Multiline = True,
            .ReadOnly = True,
            .BorderStyle = BorderStyle.None,
            .BackColor = Color.FromArgb(245, 245, 245),
            .Font = New Font("Segoe UI", 10)
        }

        Dim lblStock As New Label With {
            .Text = "Stok: " & product("stock").ToString() & " unit",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 290),
            .Size = New Size(340, 25),
            .ForeColor = If(Convert.ToInt32(product("stock")) > 0, Color.FromArgb(39, 174, 96), Color.FromArgb(231, 76, 60))
        }

        ' Quantity selector
        Dim lblQty As New Label With {
            .Text = "Jumlah:",
            .Font = New Font("Segoe UI", 10),
            .Location = New Point(20, 330),
            .Size = New Size(60, 25)
        }

        Dim numQty As New NumericUpDown With {
            .Location = New Point(85, 328),
            .Size = New Size(80, 30),
            .Minimum = 1,
            .Maximum = Math.Max(1, Convert.ToInt32(product("stock"))),
            .Value = 1,
            .Font = New Font("Segoe UI", 10),
            .Enabled = Convert.ToInt32(product("stock")) > 0
        }

        ' Buttons
        Dim btnAddCart As New Button With {
            .Text = "ADD TO CART",
            .Size = New Size(170, 40),
            .Location = New Point(20, 380),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand,
            .Enabled = Convert.ToInt32(product("stock")) > 0
        }
        btnAddCart.FlatAppearance.BorderSize = 0

        AddHandler btnAddCart.Click, Sub()
                                         AddToCart(product, Convert.ToInt32(numQty.Value))
                                         quickView.Close()
                                         MessageBox.Show("Produk berhasil ditambahkan ke keranjang!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                     End Sub

        Dim btnClose As New Button With {
            .Text = "Tutup",
            .Size = New Size(100, 40),
            .Location = New Point(200, 380),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10),
            .Cursor = Cursors.Hand
        }
        btnClose.FlatAppearance.BorderSize = 0
        AddHandler btnClose.Click, Sub() quickView.Close()

        pnlRight.Controls.AddRange({lblName, lblCategory, lblPrice, lblDescTitle, txtDesc, lblStock, lblQty, numQty, btnAddCart, btnClose})

        quickView.Controls.AddRange({pnlLeft, pnlRight})
        quickView.ShowDialog()
    End Sub

    Private Sub BtnBuy_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim product As Dictionary(Of String, Object) = CType(btn.Tag, Dictionary(Of String, Object))

        ' Quick add with quantity 1
        AddToCart(product, 1)

        ' Animation effect
        btn.Text = "✓ Ditambahkan"
        btn.BackColor = Color.FromArgb(46, 204, 113)

        Dim timer As New Timer With {.Interval = 1500}
        AddHandler timer.Tick, Sub()
                                   btn.Text = "ADD TO CART"
                                   btn.BackColor = Color.FromArgb(52, 152, 219)
                                   timer.Stop()
                                   timer.Dispose()
                               End Sub
        timer.Start()
    End Sub

    Private Sub BtnBuyNow_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim product As Dictionary(Of String, Object) = CType(btn.Tag, Dictionary(Of String, Object))

        ' Add to cart and go to checkout
        AddToCart(product, 1)
        BtnCart_Click(Nothing, Nothing)
    End Sub

    Private Sub AddToCart(product As Dictionary(Of String, Object), quantity As Integer)
        ' Check existing item
        Dim existingItem = cartItems.FirstOrDefault(Function(item) item.ProductID = Convert.ToInt32(product("product_id")))

        If existingItem IsNot Nothing Then
            existingItem.Quantity += quantity
            existingItem.Total = existingItem.Price * existingItem.Quantity
        Else
            cartItems.Add(New CartItem With {
                .ProductID = Convert.ToInt32(product("product_id")),
                .ProductName = product("product_name").ToString(),
                .Price = Convert.ToDecimal(product("price")),
                .Quantity = quantity,
                .Total = Convert.ToDecimal(product("price")) * quantity
            })
        End If

        UpdateCartButton()
    End Sub

    Private Sub UpdateCartButton()
        Dim btnCart As Button = CType(Me.Controls.Find("btnCart", True).FirstOrDefault(), Button)
        If btnCart IsNot Nothing Then
            Dim totalItems = cartItems.Sum(Function(item) item.Quantity)
            btnCart.Text = $"CART ({totalItems})"
        End If
    End Sub

    Private Sub BtnCart_Click(sender As Object, e As EventArgs)
        If cartItems.Count = 0 Then
            MessageBox.Show("Keranjang belanja kosong!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Dim frmCart As New FormCart(cartItems, currentUserID)
        If frmCart.ShowDialog() = DialogResult.OK Then
            cartItems.Clear()
            UpdateCartButton()
            LoadProducts() ' Reload untuk update stok
        End If
    End Sub

    Private Sub CategoryButton_Click(sender As Object, e As EventArgs)
        Dim btn As Button = CType(sender, Button)
        Dim categoryID As Integer = Convert.ToInt32(btn.Tag)

        ' Update button appearance
        For Each ctrl In btn.Parent.Controls
            If TypeOf ctrl Is Button AndAlso ctrl.Name.StartsWith("btnCat_") Then
                Dim b As Button = CType(ctrl, Button)
                If b.Name = btn.Name Then
                    b.BackColor = Color.FromArgb(52, 152, 219)
                    b.ForeColor = Color.White
                Else
                    b.BackColor = Color.White
                    b.ForeColor = Color.FromArgb(52, 73, 94)
                End If
            End If
        Next

        ' Get current sort
        Dim cmbSort As ComboBox = CType(Me.Controls.Find("cmbSort", True).FirstOrDefault(), ComboBox)
        Dim sortBy As String = If(cmbSort IsNot Nothing, cmbSort.Text, "Terbaru")

        LoadProducts(categoryID, "", sortBy)
    End Sub

    Private Sub BtnSearch_Click(sender As Object, e As EventArgs)
        Dim txtSearch As TextBox = CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox)
        If txtSearch IsNot Nothing Then
            Dim cmbSort As ComboBox = CType(Me.Controls.Find("cmbSort", True).FirstOrDefault(), ComboBox)
            Dim sortBy As String = If(cmbSort IsNot Nothing, cmbSort.Text, "Terbaru")
            LoadProducts(0, txtSearch.Text.Trim(), sortBy)
        End If
    End Sub

    Private Sub CmbSort_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim cmbSort As ComboBox = CType(sender, ComboBox)
        LoadProducts(0, "", cmbSort.Text)
    End Sub



    Private Sub UpdateUserInfo()
        Dim lblUserName As Label = CType(Me.Controls.Find("lblUserName", True).FirstOrDefault(), Label)
        If lblUserName IsNot Nothing Then
            lblUserName.Text = currentUserName
        End If
    End Sub

    Private Sub BannerTimer_Tick(sender As Object, e As EventArgs) Handles bannerTimer.Tick
        currentBannerIndex = (currentBannerIndex + 1) Mod bannerMessages.Length

        Dim lblBanner As Label = CType(Me.Controls.Find("lblBanner", True).FirstOrDefault(), Label)
        If lblBanner IsNot Nothing Then
            ' Fade out effect
            Dim fadeTimer As New Timer With {.Interval = 50}
            Dim alpha As Integer = 255

            AddHandler fadeTimer.Tick, Sub()
                                           alpha -= 25
                                           If alpha <= 0 Then
                                               lblBanner.Text = bannerMessages(currentBannerIndex)
                                               fadeTimer.Stop()
                                               fadeTimer.Dispose()
                                           End If
                                       End Sub

            fadeTimer.Start()
        End If
    End Sub

    Private Sub LoadWishlist()
        Try
            koneksi()

            perintahSQL = New MySqlCommand("SELECT product_id FROM wishlist WHERE user_id = @userID", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
            pembacaData = perintahSQL.ExecuteReader()

            wishlistItems.Clear()
            While pembacaData.Read()
                wishlistItems.Add(Convert.ToInt32(pembacaData("product_id")))
            End While

            pembacaData.Close()
            tutupKoneksi()

            UpdateWishlistButton()

        Catch ex As Exception
            MessageBox.Show("Error loading wishlist: " & ex.Message)
        End Try
    End Sub

    Private Sub UpdateWishlistButton()
        Dim btnWishlist As Button = CType(Me.Controls.Find("btnWishlist", True).FirstOrDefault(), Button)
        If btnWishlist IsNot Nothing Then
            btnWishlist.Text = $"WISH ({wishlistItems.Count})"
        End If
    End Sub

    Private Sub BtnProductWishlist_Click(sender As Object, e As EventArgs)
        Dim lbl As Label = CType(sender, Label)
        Dim productID As Integer = Convert.ToInt32(lbl.Tag)

        Try
            koneksi()

            If wishlistItems.Contains(productID) Then
                ' Remove from wishlist
                perintahSQL = New MySqlCommand("DELETE FROM wishlist WHERE user_id = @userID AND product_id = @productID", koneksiDatabase)
                perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
                perintahSQL.Parameters.AddWithValue("@productID", productID)
                perintahSQL.ExecuteNonQuery()

                wishlistItems.Remove(productID)
                lbl.Text = "♡"
                lbl.ForeColor = Color.FromArgb(149, 165, 166)

                ' Update hover handlers untuk status baru
                RemoveHandler lbl.MouseEnter, Nothing
                RemoveHandler lbl.MouseLeave, Nothing

                AddHandler lbl.MouseEnter, Sub(removedWishBtn, hoverEnter)
                                               lbl.Font = New Font("Segoe UI", 20, FontStyle.Bold)
                                               lbl.ForeColor = Color.FromArgb(231, 76, 60)
                                           End Sub

                AddHandler lbl.MouseLeave, Sub(removedWishBtn, hoverLeave)
                                               lbl.Font = New Font("Segoe UI", 18, FontStyle.Bold)
                                               lbl.ForeColor = Color.FromArgb(149, 165, 166)
                                           End Sub
            Else
                ' Add to wishlist
                perintahSQL = New MySqlCommand("INSERT INTO wishlist (user_id, product_id) VALUES (@userID, @productID)", koneksiDatabase)
                perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
                perintahSQL.Parameters.AddWithValue("@productID", productID)
                perintahSQL.ExecuteNonQuery()

                wishlistItems.Add(productID)
                lbl.Text = "♥"
                lbl.ForeColor = Color.FromArgb(231, 76, 60)

                ' Update hover handlers untuk status baru
                RemoveHandler lbl.MouseEnter, Nothing
                RemoveHandler lbl.MouseLeave, Nothing

                AddHandler lbl.MouseEnter, Sub(addedWishBtn, hoverEnter)
                                               lbl.Font = New Font("Segoe UI", 20, FontStyle.Bold)
                                               lbl.ForeColor = Color.FromArgb(192, 57, 43)
                                           End Sub

                AddHandler lbl.MouseLeave, Sub(addedWishBtn, hoverLeave)
                                               lbl.Font = New Font("Segoe UI", 18, FontStyle.Bold)
                                               lbl.ForeColor = Color.FromArgb(231, 76, 60)
                                           End Sub
            End If

            tutupKoneksi()
            UpdateWishlistButton()

        Catch ex As Exception
            MessageBox.Show("Error updating wishlist: " & ex.Message)
        End Try
    End Sub

    Private Sub BtnWishlist_Click(sender As Object, e As EventArgs)
        ' Show wishlist form
        Dim frmWishlist As New FormWishlist With {
            .currentUserID = Me.currentUserID,
            .currentUserName = Me.currentUserName
        }
        frmWishlist.ShowDialog()

        ' Reload wishlist after form closes to update counts
        LoadWishlist()
        LoadProducts() ' Reload products to update wishlist buttons
    End Sub



    Private Sub BtnMenu_Click(sender As Object, e As EventArgs)
        ' Create context menu
        Dim contextMenu As New ContextMenuStrip()

        Dim menuProfile As New ToolStripMenuItem("PROFILE - Profil Saya")
        AddHandler menuProfile.Click, Sub() MessageBox.Show("Fitur Profil akan segera tersedia", "Info")

        Dim menuOrders As New ToolStripMenuItem("ORDERS - Pesanan Saya")
        AddHandler menuOrders.Click, Sub()
                                         Dim frmOrders As New FormMyOrders(currentUserID)
                                         frmOrders.ShowDialog()
                                     End Sub

        ' Dim menuPaymentHistory As New ToolStripMenuItem("PAYMENT - Riwayat Pembayaran")
        ' AddHandler menuPaymentHistory.Click, Sub()
        '                                          Dim frmPaymentHistory As New FormPaymentHistory(currentUserID)
        '                                          frmPaymentHistory.ShowDialog()
        '                                      End Sub

        Dim menuSettings As New ToolStripMenuItem("SETTINGS - Pengaturan")
        AddHandler menuSettings.Click, Sub() MessageBox.Show("Fitur Pengaturan akan segera tersedia", "Info")

        contextMenu.Items.Add(menuProfile)
        contextMenu.Items.Add(menuOrders)
        ' contextMenu.Items.Add(menuPaymentHistory)
        contextMenu.Items.Add(menuSettings)

        If isAdmin Then
            contextMenu.Items.Add(New ToolStripSeparator())
            Dim menuAdmin As New ToolStripMenuItem("ADMIN - Admin Dashboard")
            AddHandler menuAdmin.Click, Sub()
                                            Dim formAdmin As New FormAdminDashboard()
                                            formAdmin.Show()
                                            Me.Hide()
                                        End Sub
            contextMenu.Items.Add(menuAdmin)
        End If

        contextMenu.Items.Add(New ToolStripSeparator())

        Dim menuLogout As New ToolStripMenuItem("LOGOUT - Logout")
        AddHandler menuLogout.Click, Sub()
                                         If MessageBox.Show("Yakin ingin logout?", "Konfirmasi", MessageBoxButtons.YesNo) = DialogResult.Yes Then
                                             Dim formLogin As New FormLogin()
                                             formLogin.Show()
                                             Me.Close()
                                         End If
                                     End Sub
        contextMenu.Items.Add(menuLogout)

        ' Show menu
        Dim btnMenu As Button = CType(sender, Button)
        contextMenu.Show(btnMenu, New Point(0, btnMenu.Height))
    End Sub

    Private Sub BtnOrders_Click(sender As Object, e As EventArgs)
        Dim frmOrders As New FormMyOrders(currentUserID)
        frmOrders.ShowDialog()
    End Sub

    ' Private Sub BtnPaymentHistory_Click(sender As Object, e As EventArgs)
    '     ShowPendingOrdersForPayment()
    ' End Sub

    Private Sub UpdateOrderStatus(orderID As Integer, newStatus As String)
        Try
            koneksi()

            ' Check if updated_at column exists
            Dim checkColumnCmd As New MySqlCommand("SHOW COLUMNS FROM orders LIKE 'updated_at'", koneksiDatabase)
            Dim columnExists As Boolean = False

            Dim columnReader As MySqlDataReader = checkColumnCmd.ExecuteReader()
            If columnReader.Read() Then
                columnExists = True
            End If
            columnReader.Close()

            ' Update order status with or without updated_at
            Dim updateQuery As String
            If columnExists Then
                updateQuery = "UPDATE orders SET order_status = @status, updated_at = NOW() WHERE order_id = @id"
            Else
                updateQuery = "UPDATE orders SET order_status = @status WHERE order_id = @id"
            End If

            perintahSQL = New MySqlCommand(updateQuery, koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@status", newStatus)
            perintahSQL.Parameters.AddWithValue("@id", orderID)
            perintahSQL.ExecuteNonQuery()

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error updating order status: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            tutupKoneksi()
        End Try
    End Sub

    Private Sub ShowPendingOrdersForPayment()
        Try
            koneksi()

            ' Debug: Check current user ID
            Console.WriteLine($"DEBUG: Checking pending orders for user ID: {currentUserID}")

            ' Get pending orders for current user
            perintahSQL = New MySqlCommand("SELECT order_id, order_date, total_amount, payment_method FROM orders WHERE user_id = @userID AND order_status = 'pending' ORDER BY order_date DESC", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)

            Dim reader As MySqlDataReader = perintahSQL.ExecuteReader()
            Dim pendingOrders As New List(Of Object)

            While reader.Read()
                pendingOrders.Add(New With {
                    .OrderID = reader("order_id"),
                    .OrderDate = reader("order_date"),
                    .TotalAmount = reader("total_amount"),
                    .PaymentMethod = reader("payment_method")
                })
            End While

            reader.Close()

            ' Debug: Check all orders for this user
            perintahSQL = New MySqlCommand("SELECT order_id, order_status, order_date FROM orders WHERE user_id = @userID ORDER BY order_date DESC LIMIT 5", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
            Dim debugReader As MySqlDataReader = perintahSQL.ExecuteReader()

            Console.WriteLine($"DEBUG: Recent orders for user {currentUserID}:")
            While debugReader.Read()
                Console.WriteLine($"Order {debugReader("order_id")}: Status = {debugReader("order_status")}, Date = {debugReader("order_date")}")
            End While
            debugReader.Close()

            tutupKoneksi()

            If pendingOrders.Count = 0 Then
                ' Show options to create dummy order or check all orders
                Dim result As DialogResult = MessageBox.Show("Tidak ada pesanan yang menunggu pembayaran." & vbCrLf & vbCrLf &
                                                           "Apakah Anda ingin:" & vbCrLf &
                                                           "• YES = Buat order dummy untuk testing" & vbCrLf &
                                                           "• NO = Lihat semua order (termasuk yang sudah selesai)",
                                                           "Tidak Ada Order Pending", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)

                If result = DialogResult.Yes Then
                    CreateDummyOrder()
                ElseIf result = DialogResult.No Then
                    ShowAllOrdersForPayment()
                End If
                Return
            End If

            ' Show pending orders selection form
            ShowPendingOrdersDialog(pendingOrders)

        Catch ex As Exception
            MessageBox.Show("Error loading pending orders: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowPendingOrdersDialog(pendingOrders As List(Of Object))
        Dim selectForm As New Form With {
            .Text = "Pilih Pesanan untuk Dibayar",
            .Size = New Size(600, 400),
            .StartPosition = FormStartPosition.CenterParent,
            .FormBorderStyle = FormBorderStyle.FixedDialog,
            .MaximizeBox = False,
            .MinimizeBox = False
        }

        Dim lblTitle As New Label With {
            .Text = "💳 PESANAN MENUNGGU PEMBAYARAN",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 152, 219),
            .Location = New Point(20, 20),
            .AutoSize = True
        }

        Dim lstOrders As New ListBox With {
            .Location = New Point(20, 60),
            .Size = New Size(540, 250),
            .Font = New Font("Segoe UI", 10)
        }

        For Each order In pendingOrders
            Dim orderText As String = $"Order #{order.OrderID} - {Convert.ToDateTime(order.OrderDate):dd/MM/yyyy HH:mm} - Rp {Convert.ToDecimal(order.TotalAmount):N0} - {order.PaymentMethod}"
            lstOrders.Items.Add(orderText)
            lstOrders.Tag = If(lstOrders.Tag, New List(Of Object)) ' Initialize if null
            CType(lstOrders.Tag, List(Of Object)).Add(order)
        Next

        Dim btnPay As New Button With {
            .Text = "💳 Bayar",
            .Size = New Size(120, 45),
            .Location = New Point(200, 300),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnPay.FlatAppearance.BorderSize = 0

        Dim btnCancel As New Button With {
            .Text = "✕ Batal",
            .Size = New Size(120, 45),
            .Location = New Point(340, 300),
            .BackColor = Color.Gray,
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0

        ' Debug button
        Dim btnDebug As New Button With {
            .Text = "🔧 Debug",
            .Size = New Size(120, 45),
            .Location = New Point(480, 300),
            .BackColor = Color.FromArgb(155, 89, 182),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnDebug.FlatAppearance.BorderSize = 0

        AddHandler btnDebug.Click, Sub()
                                       ' Show debug menu
                                       Dim debugForm As New Form With {
                                           .Text = "Debug Order Status",
                                           .Size = New Size(450, 300),
                                           .StartPosition = FormStartPosition.CenterParent,
                                           .FormBorderStyle = FormBorderStyle.FixedDialog,
                                           .MaximizeBox = False,
                                           .MinimizeBox = False
                                       }

                                       Dim btnCheckDB As New Button With {
                                           .Text = "🔍 Check Database",
                                           .Size = New Size(150, 40),
                                           .Location = New Point(20, 20),
                                           .BackColor = Color.FromArgb(52, 152, 219),
                                           .ForeColor = Color.White,
                                           .FlatStyle = FlatStyle.Flat,
                                           .Font = New Font("Segoe UI", 10, FontStyle.Bold)
                                       }

                                       Dim btnFixPending As New Button With {
                                           .Text = "🔧 Fix Pending Orders",
                                           .Size = New Size(150, 40),
                                           .Location = New Point(200, 20),
                                           .BackColor = Color.FromArgb(231, 76, 60),
                                           .ForeColor = Color.White,
                                           .FlatStyle = FlatStyle.Flat,
                                           .Font = New Font("Segoe UI", 10, FontStyle.Bold)
                                       }

                                       Dim btnTestUpdate As New Button With {
                                           .Text = "⚡ Test Update",
                                           .Size = New Size(150, 40),
                                           .Location = New Point(20, 80),
                                           .BackColor = Color.FromArgb(46, 204, 113),
                                           .ForeColor = Color.White,
                                           .FlatStyle = FlatStyle.Flat,
                                           .Font = New Font("Segoe UI", 10, FontStyle.Bold)
                                       }

                                       Dim btnResetPending As New Button With {
                                           .Text = "🔄 Reset to Pending",
                                           .Size = New Size(150, 40),
                                           .Location = New Point(200, 80),
                                           .BackColor = Color.FromArgb(155, 89, 182),
                                           .ForeColor = Color.White,
                                           .FlatStyle = FlatStyle.Flat,
                                           .Font = New Font("Segoe UI", 10, FontStyle.Bold)
                                       }

                                       Dim btnCloseDebug As New Button With {
                                           .Text = "✕ Close",
                                           .Size = New Size(150, 40),
                                           .Location = New Point(125, 140),
                                           .BackColor = Color.Gray,
                                           .ForeColor = Color.White,
                                           .FlatStyle = FlatStyle.Flat,
                                           .Font = New Font("Segoe UI", 10, FontStyle.Bold)
                                       }

                                       AddHandler btnCheckDB.Click, Sub()
                                                                        MessageBox.Show("Debug feature tidak tersedia", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                                                    End Sub

                                       AddHandler btnFixPending.Click, Sub()
                                                                           MessageBox.Show("Debug feature tidak tersedia", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                                                       End Sub

                                       AddHandler btnTestUpdate.Click, Sub()
                                                                           MessageBox.Show("Debug feature tidak tersedia", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                                                       End Sub

                                       AddHandler btnResetPending.Click, Sub()
                                                                             MessageBox.Show("Debug feature tidak tersedia", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                                                         End Sub

                                       AddHandler btnCloseDebug.Click, Sub()
                                                                           debugForm.Close()
                                                                       End Sub

                                       debugForm.Controls.AddRange({btnCheckDB, btnFixPending, btnTestUpdate, btnResetPending, btnCloseDebug})
                                       debugForm.ShowDialog()
                                   End Sub

        AddHandler btnPay.Click, Sub()
                                     If lstOrders.SelectedIndex >= 0 Then
                                         Dim selectedOrder = CType(lstOrders.Tag, List(Of Object))(lstOrders.SelectedIndex)

                                         ' Show payment status selection dialog
                                         Dim statusForm As New Form With {
                                             .Text = "Pilih Status Pembayaran",
                                             .Size = New Size(400, 300),
                                             .StartPosition = FormStartPosition.CenterParent,
                                             .FormBorderStyle = FormBorderStyle.FixedDialog,
                                             .MaximizeBox = False,
                                             .MinimizeBox = False
                                         }

                                         Dim lblOrderTitle As New Label With {
                                             .Text = $"Order #{selectedOrder.OrderID} - Rp {Convert.ToDecimal(selectedOrder.TotalAmount):N0}",
                                             .Location = New Point(20, 20),
                                             .Size = New Size(350, 30),
                                             .Font = New Font("Segoe UI", 12, FontStyle.Bold),
                                             .TextAlign = ContentAlignment.MiddleCenter
                                         }

                                         Dim lblPaymentInstruction As New Label With {
                                             .Text = "Pilih status pembayaran:",
                                             .Location = New Point(20, 60),
                                             .Size = New Size(350, 20),
                                             .Font = New Font("Segoe UI", 10)
                                         }

                                         ' Button Berhasil
                                         Dim btnSuccess As New Button With {
                                             .Text = "✅ Berhasil",
                                             .Size = New Size(100, 40),
                                             .Location = New Point(50, 100),
                                             .BackColor = Color.FromArgb(46, 204, 113),
                                             .ForeColor = Color.White,
                                             .FlatStyle = FlatStyle.Flat,
                                             .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                                             .Cursor = Cursors.Hand,
                                             .Tag = "success"
                                         }
                                         btnSuccess.FlatAppearance.BorderSize = 0

                                         ' Button Proses
                                         Dim btnProcess As New Button With {
                                             .Text = "⏳ Proses",
                                             .Size = New Size(100, 40),
                                             .Location = New Point(160, 100),
                                             .BackColor = Color.FromArgb(241, 196, 15),
                                             .ForeColor = Color.White,
                                             .FlatStyle = FlatStyle.Flat,
                                             .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                                             .Cursor = Cursors.Hand,
                                             .Tag = "processing"
                                         }
                                         btnProcess.FlatAppearance.BorderSize = 0

                                         ' Button Gagal
                                         Dim btnFailed As New Button With {
                                             .Text = "❌ Gagal",
                                             .Size = New Size(100, 40),
                                             .Location = New Point(270, 100),
                                             .BackColor = Color.FromArgb(231, 76, 60),
                                             .ForeColor = Color.White,
                                             .FlatStyle = FlatStyle.Flat,
                                             .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                                             .Cursor = Cursors.Hand,
                                             .Tag = "failed"
                                         }
                                         btnFailed.FlatAppearance.BorderSize = 0

                                         ' Button Batal
                                         Dim btnCancelStatus As New Button With {
                                             .Text = "Batal",
                                             .Size = New Size(100, 35),
                                             .Location = New Point(150, 180),
                                             .BackColor = Color.Gray,
                                             .ForeColor = Color.White,
                                             .FlatStyle = FlatStyle.Flat,
                                             .Font = New Font("Segoe UI", 10),
                                             .Cursor = Cursors.Hand
                                         }
                                         btnCancelStatus.FlatAppearance.BorderSize = 0

                                         statusForm.Controls.AddRange({lblOrderTitle, lblPaymentInstruction, btnSuccess, btnProcess, btnFailed, btnCancelStatus})

                                         ' Event handlers for status buttons
                                         AddHandler btnSuccess.Click, Sub()
                                                                          ' Langsung update ke delivered (berhasil)
                                                                          OrderStatusManager.Instance.UpdateOrderStatusBasedOnUserAction(selectedOrder.OrderID, "payment_success")
                                                                          statusForm.DialogResult = DialogResult.OK
                                                                          statusForm.Close()
                                                                      End Sub

                                         AddHandler btnProcess.Click, Sub()
                                                                          ' Update ke processing (proses)
                                                                          OrderStatusManager.Instance.UpdateOrderStatusBasedOnUserAction(selectedOrder.OrderID, "payment_processing")
                                                                          statusForm.DialogResult = DialogResult.OK
                                                                          statusForm.Close()
                                                                      End Sub

                                         AddHandler btnFailed.Click, Sub()
                                                                         UpdateOrderStatus(selectedOrder.OrderID, "pending")
                                                                         statusForm.DialogResult = DialogResult.Cancel
                                                                         statusForm.Close()
                                                                     End Sub

                                         AddHandler btnCancelStatus.Click, Sub()
                                                                               statusForm.DialogResult = DialogResult.Cancel
                                                                               statusForm.Close()
                                                                           End Sub

                                         selectForm.Hide()
                                         statusForm.ShowDialog()
                                         selectForm.Close()
                                     Else
                                         MessageBox.Show("Pilih pesanan yang akan dibayar!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                                     End If
                                 End Sub

        AddHandler btnCancel.Click, Sub() selectForm.Close()

        selectForm.Controls.AddRange({lblTitle, lstOrders, btnPay, btnCancel, btnDebug})
        selectForm.ShowDialog()
    End Sub

    Private Sub CreateDummyOrder()
        Try
            koneksi()

            ' Create dummy order for testing
            perintahSQL = New MySqlCommand("INSERT INTO orders (user_id, order_date, total_amount, order_status, shipping_address, payment_method) " &
                                  "VALUES (@userID, @orderDate, @totalAmount, @status, @address, @payment)", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)
            perintahSQL.Parameters.AddWithValue("@orderDate", DateTime.Now)
            perintahSQL.Parameters.AddWithValue("@totalAmount", 50000)
            perintahSQL.Parameters.AddWithValue("@status", "pending")
            perintahSQL.Parameters.AddWithValue("@address", "Alamat Testing - Jl. Test No. 123")
            perintahSQL.Parameters.AddWithValue("@payment", "Transfer Bank")
            perintahSQL.ExecuteNonQuery()

            Dim orderID As Integer = Convert.ToInt32(perintahSQL.LastInsertedId)

            ' Add dummy order details
            perintahSQL = New MySqlCommand("INSERT INTO order_details (order_id, product_id, quantity, price, subtotal) " &
                                  "VALUES (@orderID, 1, 1, 50000, 50000)", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@orderID", orderID)
            perintahSQL.ExecuteNonQuery()

            tutupKoneksi()

            MessageBox.Show($"Order dummy #{orderID} berhasil dibuat untuk testing!" & vbCrLf &
                           "Silakan coba lagi tombol Bayar.", "Order Dummy Dibuat", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show("Error creating dummy order: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            tutupKoneksi()
        End Try
    End Sub

    Private Sub ShowAllOrdersForPayment()
        Try
            koneksi()

            ' Get all orders for current user (not just pending)
            perintahSQL = New MySqlCommand("SELECT order_id, order_date, total_amount, payment_method, order_status FROM orders WHERE user_id = @userID ORDER BY order_date DESC", koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)

            Dim reader As MySqlDataReader = perintahSQL.ExecuteReader()
            Dim allOrders As New List(Of Object)

            While reader.Read()
                allOrders.Add(New With {
                    .OrderID = reader("order_id"),
                    .OrderDate = reader("order_date"),
                    .TotalAmount = reader("total_amount"),
                    .PaymentMethod = reader("payment_method"),
                    .OrderStatus = reader("order_status")
                })
            End While

            reader.Close()
            tutupKoneksi()

            If allOrders.Count = 0 Then
                MessageBox.Show("Tidak ada order sama sekali. Silakan buat order baru melalui checkout.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            ' Show all orders selection form (modified)
            ShowAllOrdersDialog(allOrders)

        Catch ex As Exception
            MessageBox.Show("Error loading all orders: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowAllOrdersDialog(allOrders As List(Of Object))
        Dim selectForm As New Form With {
            .Text = "Semua Pesanan (Untuk Testing)",
            .Size = New Size(700, 400),
            .StartPosition = FormStartPosition.CenterParent,
            .FormBorderStyle = FormBorderStyle.FixedDialog,
            .MaximizeBox = False,
            .MinimizeBox = False
        }

        Dim lblTitle As New Label With {
            .Text = "📋 SEMUA PESANAN (TERMASUK YANG SUDAH SELESAI)",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(231, 76, 60),
            .Location = New Point(20, 20),
            .AutoSize = True
        }

        Dim lstOrders As New ListBox With {
            .Location = New Point(20, 60),
            .Size = New Size(640, 250),
            .Font = New Font("Segoe UI", 10)
        }

        For Each order In allOrders
            Dim statusColor As String = ""
            Select Case order.OrderStatus.ToString().ToLower()
                Case "pending"
                    statusColor = "🟡"
                Case "processing"
                    statusColor = "🔵"
                Case "success"
                    statusColor = "🟢"
                Case "cancelled"
                    statusColor = "🔴"
                Case Else
                    statusColor = "⚪"
            End Select

            Dim orderText As String = $"{statusColor} Order #{order.OrderID} - {Convert.ToDateTime(order.OrderDate):dd/MM/yyyy HH:mm} - Rp {Convert.ToDecimal(order.TotalAmount):N0} - {order.PaymentMethod} - [{order.OrderStatus}]"
            lstOrders.Items.Add(orderText)
            lstOrders.Tag = If(lstOrders.Tag, New List(Of Object))
            CType(lstOrders.Tag, List(Of Object)).Add(order)
        Next

        Dim btnChangeStatus As New Button With {
            .Text = "🔄 Ubah Status",
            .Size = New Size(150, 45),
            .Location = New Point(200, 320),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnChangeStatus.FlatAppearance.BorderSize = 0

        Dim btnClose As New Button With {
            .Text = "✕ Tutup",
            .Size = New Size(120, 45),
            .Location = New Point(370, 320),
            .BackColor = Color.Gray,
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnClose.FlatAppearance.BorderSize = 0

        AddHandler btnChangeStatus.Click, Sub()
                                              If lstOrders.SelectedIndex >= 0 Then
                                                  Dim selectedOrder = CType(lstOrders.Tag, List(Of Object))(lstOrders.SelectedIndex)

                                                  ' Show status change options
                                                  Dim statusOptions As String() = {"pending", "processing", "success", "cancelled"}
                                                  Dim selectedStatus As String = InputBox($"Ubah status Order #{selectedOrder.OrderID} ke:" & vbCrLf & vbCrLf &
                                                                                       "pending = Menunggu pembayaran" & vbCrLf &
                                                                                       "processing = Sedang diproses" & vbCrLf &
                                                                                       "success = Berhasil/Selesai" & vbCrLf &
                                                                                       "cancelled = Dibatalkan",
                                                                                       "Ubah Status", selectedOrder.OrderStatus.ToString())

                                                  If Not String.IsNullOrEmpty(selectedStatus) AndAlso statusOptions.Contains(selectedStatus.ToLower()) Then
                                                      UpdateOrderStatus(selectedOrder.OrderID, selectedStatus.ToLower())
                                                      selectForm.Close()
                                                      ShowAllOrdersForPayment() ' Refresh
                                                  End If
                                              Else
                                                  MessageBox.Show("Pilih order terlebih dahulu!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information)
                                              End If
                                          End Sub

        AddHandler btnClose.Click, Sub() selectForm.Close()

        selectForm.Controls.AddRange({lblTitle, lstOrders, btnChangeStatus, btnClose})
        selectForm.ShowDialog()
    End Sub
End Class