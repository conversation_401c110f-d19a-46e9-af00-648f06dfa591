﻿' FormViewProducts.vb - Form untuk Melihat Daftar Produk
Imports MySql.Data.MySqlClient

Public Class FormViewProducts
    Private Sub FormViewProducts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Daftar Produk"
        Me.Size = New Size(1000, 600)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(245, 245, 245)

        CreateUI()
        LoadProducts()
        SetupDataGridView()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.FromArgb(52, 152, 219)
        }

        Dim lblTitle As New Label With {
            .Text = "DAFTAR PRODUK",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 15),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)

        ' Search Panel
        Dim pnlSearch As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 70,
            .BackColor = Color.White,
            .Padding = New Padding(20)
        }

        Dim lblSearch As New Label With {
            .Text = "Search - Cari Produk:",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(20, 23),
            .AutoSize = True
        }

        Dim txtSearch As New TextBox With {
            .Name = "txtSearch",
            .Size = New Size(300, 30),
            .Location = New Point(140, 20),
            .Font = New Font("Segoe UI", 11)
        }
        AddHandler txtSearch.TextChanged, AddressOf TxtSearch_TextChanged

        ' Category Filter
        Dim lblFilter As New Label With {
            .Text = "Kategori:",
            .Font = New Font("Segoe UI", 11),
            .Location = New Point(460, 23),
            .AutoSize = True
        }

        Dim cmbCategoryFilter As New ComboBox With {
            .Name = "cmbCategoryFilter",
            .Size = New Size(200, 30),
            .Location = New Point(530, 20),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 11)
        }
        AddHandler cmbCategoryFilter.SelectedIndexChanged, AddressOf CmbCategoryFilter_Changed

        ' Refresh Button
        Dim btnRefresh As New Button With {
            .Text = "Refresh",
            .Size = New Size(100, 30),
            .Location = New Point(750, 20),
            .BackColor = Color.FromArgb(39, 174, 96),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, Sub()
                                         LoadProducts()
                                         CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox).Clear()
                                         CType(Me.Controls.Find("cmbCategoryFilter", True).FirstOrDefault(), ComboBox).SelectedIndex = 0
                                     End Sub

        pnlSearch.Controls.AddRange({lblSearch, txtSearch, lblFilter, cmbCategoryFilter, btnRefresh})

        ' Main Panel
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(20)
        }

        ' DataGridView
        Dim dgvProducts As New DataGridView With {
            .Name = "dgvProducts",
            .Dock = DockStyle.Fill,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .ReadOnly = True,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .RowHeadersVisible = False,
            .DefaultCellStyle = New DataGridViewCellStyle With {
                .Font = New Font("Segoe UI", 10),
                .SelectionBackColor = Color.FromArgb(52, 152, 219),
                .Padding = New Padding(5)
            },
            .ColumnHeadersDefaultCellStyle = New DataGridViewCellStyle With {
                .Font = New Font("Segoe UI", 11, FontStyle.Bold),
                .BackColor = Color.FromArgb(44, 62, 80),
                .ForeColor = Color.White,
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .RowTemplate = New DataGridViewRow With {
                .Height = 40
            }
        }
        AddHandler dgvProducts.CellDoubleClick, AddressOf DgvProducts_CellDoubleClick

        pnlMain.Controls.Add(dgvProducts)

        ' Bottom Panel
        Dim pnlBottom As New Panel With {
            .Dock = DockStyle.Bottom,
            .Height = 60,
            .BackColor = Color.White
        }

        ' Action Buttons
        Dim btnAdd As New Button With {
            .Text = "ADD - Tambah Produk",
            .Size = New Size(160, 40),
            .Location = New Point(20, 10),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnAdd.FlatAppearance.BorderSize = 0
        AddHandler btnAdd.Click, AddressOf BtnAdd_Click

        Dim btnEdit As New Button With {
            .Text = "EDIT - Edit Produk",
            .Size = New Size(160, 40),
            .Location = New Point(190, 10),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnEdit.FlatAppearance.BorderSize = 0
        AddHandler btnEdit.Click, AddressOf BtnEdit_Click

        Dim btnDelete As New Button With {
            .Text = "DELETE - Hapus Produk",
            .Size = New Size(170, 40),
            .Location = New Point(360, 10),
            .BackColor = Color.FromArgb(231, 76, 60),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnDelete.FlatAppearance.BorderSize = 0
        AddHandler btnDelete.Click, AddressOf BtnDelete_Click

        ' Product Count Label
        Dim lblCount As New Label With {
            .Name = "lblProductCount",
            .Text = "Total: 0 produk",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(Me.Width - 200, 20),
            .AutoSize = True
        }

        pnlBottom.Controls.AddRange({btnAdd, btnEdit, btnDelete, lblCount})

        Me.Controls.AddRange({pnlMain, pnlBottom, pnlSearch, pnlHeader})
    End Sub

    Private Sub SetupDataGridView()
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)
        If dgv IsNot Nothing Then
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245)
            dgv.EnableHeadersVisualStyles = False
        End If
    End Sub

    Private Sub LoadProducts(Optional searchText As String = "", Optional categoryFilter As String = "")
        Try
            koneksi()

            ' Load categories for filter
            If CType(Me.Controls.Find("cmbCategoryFilter", True).FirstOrDefault(), ComboBox).Items.Count = 0 Then
                LoadCategoryFilter()
            End If

            Dim query As String = "SELECT p.product_id, p.product_name, c.category_name, p.price, p.stock, p.description " &
                                 "FROM products p JOIN categories c ON p.category_id = c.category_id WHERE 1=1 "

            If searchText <> "" Then
                query &= "AND p.product_name LIKE '%" & searchText & "%' "
            End If

            If categoryFilter <> "" AndAlso categoryFilter <> "Semua Kategori" Then
                query &= "AND c.category_name = '" & categoryFilter & "' "
            End If

            query &= "ORDER BY p.product_id DESC"

            adapterData = New MySqlDataAdapter(query, koneksiDatabase)
            Dim dt As New DataTable()
            adapterData.Fill(dt)

            Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)
            dgv.DataSource = dt

            ' Format columns
            dgv.Columns("product_id").HeaderText = "ID"
            dgv.Columns("product_id").Width = 60
            dgv.Columns("product_name").HeaderText = "Nama Produk"
            dgv.Columns("category_name").HeaderText = "Kategori"
            dgv.Columns("category_name").Width = 150
            dgv.Columns("price").HeaderText = "Harga"
            dgv.Columns("price").DefaultCellStyle.Format = "Rp #,##0"
            dgv.Columns("price").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
            dgv.Columns("price").Width = 120
            dgv.Columns("stock").HeaderText = "Stok"
            dgv.Columns("stock").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
            dgv.Columns("stock").Width = 80
            dgv.Columns("description").HeaderText = "Deskripsi"

            ' Color code stock
            For Each row As DataGridViewRow In dgv.Rows
                Dim stock As Integer = Convert.ToInt32(row.Cells("stock").Value)
                If stock = 0 Then
                    row.Cells("stock").Style.ForeColor = Color.Red
                    row.Cells("stock").Style.Font = New Font("Segoe UI", 10, FontStyle.Bold)
                ElseIf stock < 10 Then
                    row.Cells("stock").Style.ForeColor = Color.Orange
                End If
            Next

            ' Update count
            Dim lblCount As Label = CType(Me.Controls.Find("lblProductCount", True).FirstOrDefault(), Label)
            If lblCount IsNot Nothing Then
                lblCount.Text = $"Total: {dt.Rows.Count} produk"
            End If

            tutupKoneksi()

        Catch ex As Exception
            MessageBox.Show("Error loading products: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub LoadCategoryFilter()
        Try
            Dim cmbFilter As ComboBox = CType(Me.Controls.Find("cmbCategoryFilter", True).FirstOrDefault(), ComboBox)
            cmbFilter.Items.Clear()
            cmbFilter.Items.Add("Semua Kategori")

            perintahSQL = New MySqlCommand("SELECT DISTINCT category_name FROM categories ORDER BY category_name", koneksiDatabase)
            pembacaData = perintahSQL.ExecuteReader()

            While pembacaData.Read()
                cmbFilter.Items.Add(pembacaData("category_name").ToString())
            End While

            pembacaData.Close()
            cmbFilter.SelectedIndex = 0

        Catch ex As Exception
            MessageBox.Show("Error loading categories: " & ex.Message)
        End Try
    End Sub

    Private Sub TxtSearch_TextChanged(sender As Object, e As EventArgs)
        Dim searchText As String = CType(sender, TextBox).Text.Trim()
        Dim categoryFilter As String = CType(Me.Controls.Find("cmbCategoryFilter", True).FirstOrDefault(), ComboBox).Text
        LoadProducts(searchText, categoryFilter)
    End Sub

    Private Sub CmbCategoryFilter_Changed(sender As Object, e As EventArgs)
        Dim searchText As String = CType(Me.Controls.Find("txtSearch", True).FirstOrDefault(), TextBox).Text.Trim()
        Dim categoryFilter As String = CType(sender, ComboBox).Text
        LoadProducts(searchText, categoryFilter)
    End Sub

    Private Sub DgvProducts_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim formAdd As New FormAddProduct()
        If formAdd.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub

    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)

        If dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih produk yang akan diedit!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim productID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("product_id").Value)
        Dim formEdit As New FormEditProduct(productID)
        If formEdit.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub

    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        Dim dgv As DataGridView = CType(Me.Controls.Find("dgvProducts", True).FirstOrDefault(), DataGridView)

        If dgv.SelectedRows.Count = 0 Then
            MessageBox.Show("Pilih produk yang akan dihapus!", "Peringatan", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Dim productID As Integer = Convert.ToInt32(dgv.SelectedRows(0).Cells("product_id").Value)
        Dim productName As String = dgv.SelectedRows(0).Cells("product_name").Value.ToString()

        Dim formDelete As New FormDeleteProduct(productID, productName)
        If formDelete.ShowDialog() = DialogResult.OK Then
            LoadProducts()
        End If
    End Sub
End Class