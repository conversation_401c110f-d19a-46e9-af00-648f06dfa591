-- Script SQL untuk membuat tabel orders dan order_details
-- Jalankan script ini di MySQL untuk membuat tabel yang diperlukan

USE db_ecommerce;

-- <PERSON><PERSON> orders untuk menyimpan data pesanan
CREATE TABLE IF NOT EXISTS orders (
    order_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    order_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(15,2) NOT NULL,
    order_status ENUM('pending', 'processing', 'shipped', 'success', 'cancelled') DEFAULT 'pending',
    shipping_address TEXT NOT NULL,
    payment_method VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Tabel order_details untuk menyimpan detail item dalam pesanan
CREATE TABLE IF NOT EXISTS order_details (
    order_detail_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Tambahkan kolom address ke tabel users jika belum ada
ALTER TABLE users ADD COLUMN IF NOT EXISTS address TEXT;

-- Index untuk performa yang lebih baik
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(order_status);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_order_details_order_id ON order_details(order_id);
CREATE INDEX idx_order_details_product_id ON order_details(product_id);

-- Insert sample data untuk testing (opsional)
-- Uncomment baris di bawah jika ingin menambahkan data sample

/*
-- Sample orders (pastikan user_id dan product_id sudah ada)
INSERT INTO orders (user_id, order_date, total_amount, order_status, shipping_address, payment_method) VALUES
(1, '2024-01-15 10:30:00', 150000, 'delivered', 'Jl. Merdeka No. 123, Jakarta Pusat, DKI Jakarta 10110', 'Transfer Bank'),
(1, '2024-01-20 14:15:00', 75000, 'shipped', 'Jl. Merdeka No. 123, Jakarta Pusat, DKI Jakarta 10110', 'COD'),
(2, '2024-01-22 09:45:00', 200000, 'processing', 'Jl. Sudirman No. 456, Bandung, Jawa Barat 40111', 'E-Wallet');

-- Sample order details (pastikan order_id dan product_id sudah ada)
INSERT INTO order_details (order_id, product_id, quantity, price, subtotal) VALUES
(1, 1, 2, 50000, 100000),
(1, 2, 1, 50000, 50000),
(2, 3, 1, 75000, 75000),
(3, 1, 4, 50000, 200000);
*/

-- Tampilkan struktur tabel yang telah dibuat
DESCRIBE orders;
DESCRIBE order_details;
