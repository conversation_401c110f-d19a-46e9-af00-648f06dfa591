# Modules Directory

Folder ini berisi modul-modul dan kelas utilitas yang digunakan di seluruh aplikasi.

## 📁 File Modules

### 🔌 Koneksi Database
- `ModulKoneksi.*` - Modul untuk mengelola koneksi database

### 📊 Status Manager
- `OrderStatusManager.vb` - Kelas untuk mengelola status pesanan

## 🎯 Cara Penggunaan

### ModulKoneksi

```vb
' Import modul
Imports ModulKoneksi

' Menggunakan koneksi
Sub MyMethod()
    Try
        koneksi()
        ' Kode database Anda di sini
        ' ...
        tutupKoneksi()
    Catch ex As Exception
        MessageBox.Show("Error: " & ex.Message)
    End Try
End Sub
```

### OrderStatusManager

```vb
' Membuat instance
Dim statusManager As New OrderStatusManager()

' Update status pesanan
statusManager.UpdateOrderStatusBasedOnUserAction(orderID, "payment_success")

' Mendapatkan estimasi waktu status berikutnya
Dim estimatedTime As String = statusManager.GetEstimatedNextStatusTime(orderID)
```

## 🔄 Status Order Flow

1. **pending** → Pesanan baru dibuat
2. **processing** → Pembayaran berhasil
3. **shipped** → Pesanan dikirim
4. **success** → Pesanan selesai
5. **cancelled** → Pesanan dibatalkan

## ⚠️ Catatan Penting

- `ModulKoneksi` harus digunakan di semua form yang memerlukan akses database
- Selalu tutup koneksi setelah selesai menggunakan database
- `OrderStatusManager` menangani perubahan status secara otomatis dan manual
- Pastikan timer untuk auto-update status berjalan di background
