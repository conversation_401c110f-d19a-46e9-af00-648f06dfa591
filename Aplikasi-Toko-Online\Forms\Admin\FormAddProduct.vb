﻿' FormAddProduct.vb - Form untuk Menambah Produk Baru
Imports MySql.Data.MySqlClient
Imports System.IO

Public Class FormAddProduct
    Private currentImagePath As String = ""

    Private Sub FormAddProduct_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Tambah Produk Baru"
        Me.Size = New Size(500, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.BackColor = Color.White

        CreateUI()
        LoadCategories()
    End Sub

    Private Sub CreateUI()
        ' Header Panel
        Dim pnlHeader As New Panel With {
            .Dock = DockStyle.Top,
            .Height = 60,
            .BackColor = Color.FromArgb(46, 204, 113)
        }

        Dim lblTitle As New Label With {
            .Text = "+ TAMBAH PRODUK BARU",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(20, 15),
            .AutoSize = True
        }

        pnlHeader.Controls.Add(lblTitle)

        ' Main Panel
        Dim pnlMain As New Panel With {
            .Dock = DockStyle.Fill,
            .Padding = New Padding(30)
        }

        Dim yPos As Integer = 20

        ' Product Name
        Dim lblName As New Label With {
            .Text = "Nama Produk *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtName As New TextBox With {
            .Name = "txtProductName",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Category
        Dim lblCategory As New Label With {
            .Text = "Kategori *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim cmbCategory As New ComboBox With {
            .Name = "cmbCategory",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Price
        Dim lblPrice As New Label With {
            .Text = "Harga (Rp) *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtPrice As New TextBox With {
            .Name = "txtPrice",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Stock
        Dim lblStock As New Label With {
            .Text = "Stok *",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtStock As New TextBox With {
            .Name = "txtStock",
            .Size = New Size(400, 30),
            .Location = New Point(30, yPos),
            .Font = New Font("Segoe UI", 11)
        }
        yPos += 40

        ' Description
        Dim lblDesc As New Label With {
            .Text = "Deskripsi",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        Dim txtDesc As New TextBox With {
            .Name = "txtDescription",
            .Size = New Size(400, 80),
            .Location = New Point(30, yPos),
            .Multiline = True,
            .Font = New Font("Segoe UI", 10),
            .ScrollBars = ScrollBars.Vertical
        }
        yPos += 90

        ' Image Section
        Dim lblImage As New Label With {
            .Text = "Gambar Produk",
            .Location = New Point(30, yPos),
            .AutoSize = True,
            .Font = New Font("Segoe UI", 10)
        }
        yPos += 25

        ' Image Preview
        Dim picProduct As New PictureBox With {
            .Name = "picProduct",
            .Size = New Size(120, 120),
            .Location = New Point(30, yPos),
            .BackColor = Color.FromArgb(236, 240, 241),
            .SizeMode = PictureBoxSizeMode.Zoom,
            .BorderStyle = BorderStyle.FixedSingle
        }

        ' Default placeholder
        Dim lblPlaceholder As New Label With {
            .Text = "IMAGE",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(189, 195, 199),
            .Dock = DockStyle.Fill,
            .TextAlign = ContentAlignment.MiddleCenter
        }
        picProduct.Controls.Add(lblPlaceholder)

        ' Browse Button
        Dim btnBrowse As New Button With {
            .Text = "Pilih Gambar",
            .Size = New Size(120, 35),
            .Location = New Point(160, yPos),
            .BackColor = Color.FromArgb(52, 152, 219),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9),
            .Cursor = Cursors.Hand
        }
        btnBrowse.FlatAppearance.BorderSize = 0
        AddHandler btnBrowse.Click, AddressOf BtnBrowse_Click

        yPos += 130

        ' Buttons
        Dim btnSave As New Button With {
            .Text = "SIMPAN",
            .Size = New Size(190, 45),
            .Location = New Point(30, yPos),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnSave.FlatAppearance.BorderSize = 0
        AddHandler btnSave.Click, AddressOf BtnSave_Click

        Dim btnCancel As New Button With {
            .Text = "BATAL",
            .Size = New Size(190, 45),
            .Location = New Point(240, yPos),
            .BackColor = Color.FromArgb(149, 165, 166),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnCancel.FlatAppearance.BorderSize = 0
        AddHandler btnCancel.Click, Sub() Me.Close()

        pnlMain.Controls.AddRange({lblName, txtName, lblCategory, cmbCategory,
                                  lblPrice, txtPrice, lblStock, txtStock,
                                  lblDesc, txtDesc, lblImage, picProduct,
                                  btnBrowse, btnSave, btnCancel})

        Me.Controls.AddRange({pnlMain, pnlHeader})
    End Sub

    Private Sub LoadCategories()
        Try
            koneksi()

            perintahSQL = New MySqlCommand("SELECT category_id, category_name FROM categories ORDER BY category_name", koneksiDatabase)
            pembacaData = perintahSQL.ExecuteReader()

            Dim cmb As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
            cmb.Items.Clear()

            Dim categories As New Dictionary(Of String, Integer)

            While pembacaData.Read()
                categories.Add(pembacaData("category_name").ToString(), Convert.ToInt32(pembacaData("category_id")))
            End While

            pembacaData.Close()
            tutupKoneksi()

            cmb.DataSource = New BindingSource(categories, Nothing)
            cmb.DisplayMember = "Key"
            cmb.ValueMember = "Value"

        Catch ex As Exception
            MessageBox.Show("Error loading categories: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BtnBrowse_Click(sender As Object, e As EventArgs)
        Dim openFileDialog As New OpenFileDialog With {
            .Filter = "Image Files|*.jpg;*.jpeg;*.png;*.gif;*.bmp",
            .Title = "Pilih Gambar Produk"
        }

        If openFileDialog.ShowDialog() = DialogResult.OK Then
            currentImagePath = openFileDialog.FileName

            Dim picProduct As PictureBox = CType(Me.Controls.Find("picProduct", True).FirstOrDefault(), PictureBox)
            Try
                picProduct.Image = Image.FromFile(currentImagePath)
                picProduct.Controls.Clear()
            Catch ex As Exception
                MessageBox.Show("Error loading image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If Not ValidateInput() Then Return

        Try
            koneksi()

            ' Save image
            Dim imagePath As String = SaveProductImage()

            Dim categoryID As Integer = CType(CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox).SelectedValue, Integer)

            perintahSQL = New MySqlCommand("INSERT INTO products (product_name, category_id, price, stock, description, image_url) " &
                                  "VALUES (@name, @category, @price, @stock, @desc, @image)", koneksiDatabase)

            perintahSQL.Parameters.AddWithValue("@name", CType(Me.Controls.Find("txtProductName", True).FirstOrDefault(), TextBox).Text.Trim())
            perintahSQL.Parameters.AddWithValue("@category", categoryID)
            perintahSQL.Parameters.AddWithValue("@price", Convert.ToDecimal(CType(Me.Controls.Find("txtPrice", True).FirstOrDefault(), TextBox).Text))
            perintahSQL.Parameters.AddWithValue("@stock", Convert.ToInt32(CType(Me.Controls.Find("txtStock", True).FirstOrDefault(), TextBox).Text))
            perintahSQL.Parameters.AddWithValue("@desc", CType(Me.Controls.Find("txtDescription", True).FirstOrDefault(), TextBox).Text.Trim())
            perintahSQL.Parameters.AddWithValue("@image", imagePath)

            perintahSQL.ExecuteNonQuery()
            tutupKoneksi()

            MessageBox.Show("Produk berhasil ditambahkan!", "Sukses", MessageBoxButtons.OK, MessageBoxIcon.Information)

            Me.DialogResult = DialogResult.OK
            Me.Close()

        Catch ex As Exception
            MessageBox.Show("Error: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Function SaveProductImage() As String
        If currentImagePath = "" OrElse Not File.Exists(currentImagePath) Then
            Return ""
        End If

        Try
            ' Create folder
            Dim imagesFolder As String = Path.Combine(Application.StartupPath, "Images", "Products")
            If Not Directory.Exists(imagesFolder) Then
                Directory.CreateDirectory(imagesFolder)
            End If

            ' Generate filename
            Dim extension As String = Path.GetExtension(currentImagePath)
            Dim newFileName As String = $"product_{DateTime.Now:yyyyMMddHHmmss}{extension}"
            Dim newPath As String = Path.Combine(imagesFolder, newFileName)

            ' Copy file
            File.Copy(currentImagePath, newPath, True)

            Return newPath

        Catch ex As Exception
            MessageBox.Show("Error saving image: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return ""
        End Try
    End Function

    Private Function ValidateInput() As Boolean
        Dim txtName As TextBox = CType(Me.Controls.Find("txtProductName", True).FirstOrDefault(), TextBox)
        Dim cmbCategory As ComboBox = CType(Me.Controls.Find("cmbCategory", True).FirstOrDefault(), ComboBox)
        Dim txtPrice As TextBox = CType(Me.Controls.Find("txtPrice", True).FirstOrDefault(), TextBox)
        Dim txtStock As TextBox = CType(Me.Controls.Find("txtStock", True).FirstOrDefault(), TextBox)

        If txtName.Text.Trim() = "" Then
            MessageBox.Show("Nama produk harus diisi!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtName.Focus()
            Return False
        End If

        If cmbCategory.SelectedIndex < 0 Then
            MessageBox.Show("Pilih kategori produk!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbCategory.Focus()
            Return False
        End If

        Try
            Dim price As Decimal = Convert.ToDecimal(txtPrice.Text)
            If price <= 0 Then
                MessageBox.Show("Harga harus lebih dari 0!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtPrice.Focus()
                Return False
            End If
        Catch
            MessageBox.Show("Harga harus berupa angka!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPrice.Focus()
            Return False
        End Try

        Try
            Dim stock As Integer = Convert.ToInt32(txtStock.Text)
            If stock < 0 Then
                MessageBox.Show("Stok tidak boleh negatif!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                txtStock.Focus()
                Return False
            End If
        Catch
            MessageBox.Show("Stok harus berupa angka!", "Validasi", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtStock.Focus()
            Return False
        End Try

        Return True
    End Function
End Class